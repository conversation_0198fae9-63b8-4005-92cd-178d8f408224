"use strict";
/**
 * FCM React Hooks
 *
 * React hooks for Firebase Cloud Messaging functionality
 * including permission management, token handling, and notifications.
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.useFCM = useFCM;
exports.useNotificationPermission = useNotificationPermission;
exports.useNotificationPreferences = useNotificationPreferences;
var react_1 = require("react");
var messaging_1 = require("firebase/messaging");
var app_1 = require("firebase/app");
var config_1 = require("../config");
// ========================================
// MAIN FCM HOOK
// ========================================
/**
 * Main FCM hook for managing Firebase Cloud Messaging
 */
function useFCM(config) {
    var _this = this;
    var _a = (0, react_1.useState)({
        isInitialized: false,
        isSupported: false,
        permission: null,
        token: null,
        error: null,
        isLoading: true,
    }), state = _a[0], setState = _a[1];
    var messagingRef = (0, react_1.useRef)(null);
    var unsubscribeRef = (0, react_1.useRef)(null);
    // Initialize Firebase and FCM
    var initialize = (0, react_1.useCallback)(function () { return __awaiter(_this, void 0, void 0, function () {
        var app, existingApps, messaging, swError_1, permission_1, unsubscribe, error_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 5, , 6]);
                    setState(function (prev) { return (__assign(__assign({}, prev), { isLoading: true, error: null })); });
                    // Check if running in browser
                    if (typeof window === 'undefined') {
                        setState(function (prev) { return (__assign(__assign({}, prev), { isSupported: false, isLoading: false, error: 'FCM is not supported in server-side environment' })); });
                        return [2 /*return*/];
                    }
                    // Check if notifications are supported
                    if (!('Notification' in window) || !('serviceWorker' in navigator)) {
                        setState(function (prev) { return (__assign(__assign({}, prev), { isSupported: false, isLoading: false, error: 'Notifications or Service Workers not supported' })); });
                        return [2 /*return*/];
                    }
                    app = void 0;
                    existingApps = (0, app_1.getApps)();
                    if (existingApps.length > 0) {
                        app = existingApps[0];
                    }
                    else {
                        app = (0, app_1.initializeApp)(config);
                    }
                    messaging = (0, messaging_1.getMessaging)(app);
                    messagingRef.current = messaging;
                    if (!('serviceWorker' in navigator)) return [3 /*break*/, 4];
                    _a.label = 1;
                case 1:
                    _a.trys.push([1, 3, , 4]);
                    return [4 /*yield*/, navigator.serviceWorker.register(config_1.FCM_SETTINGS.SERVICE_WORKER_PATH, {
                            scope: config_1.FCM_SETTINGS.SERVICE_WORKER_SCOPE,
                        })];
                case 2:
                    _a.sent();
                    return [3 /*break*/, 4];
                case 3:
                    swError_1 = _a.sent();
                    console.warn('Service worker registration failed:', swError_1);
                    return [3 /*break*/, 4];
                case 4:
                    permission_1 = Notification.permission;
                    setState(function (prev) { return (__assign(__assign({}, prev), { isInitialized: true, isSupported: true, permission: permission_1, isLoading: false })); });
                    unsubscribe = (0, messaging_1.onMessage)(messaging, function (payload) {
                        console.log('Foreground message received:', payload);
                        // Handle foreground notifications
                        if (payload.notification) {
                            showNotification(payload.notification);
                        }
                    });
                    unsubscribeRef.current = unsubscribe;
                    return [3 /*break*/, 6];
                case 5:
                    error_1 = _a.sent();
                    console.error('FCM initialization error:', error_1);
                    setState(function (prev) { return (__assign(__assign({}, prev), { error: error_1 instanceof Error ? error_1.message : 'Failed to initialize FCM', isLoading: false })); });
                    return [3 /*break*/, 6];
                case 6: return [2 /*return*/];
            }
        });
    }); }, [config]);
    // Request notification permission
    var requestPermission = (0, react_1.useCallback)(function () { return __awaiter(_this, void 0, void 0, function () {
        var permission_2, error_2;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    if (!state.isSupported) {
                        throw new Error('Notifications not supported');
                    }
                    return [4 /*yield*/, Notification.requestPermission()];
                case 1:
                    permission_2 = _a.sent();
                    setState(function (prev) { return (__assign(__assign({}, prev), { permission: permission_2 })); });
                    return [2 /*return*/, permission_2 === 'granted'];
                case 2:
                    error_2 = _a.sent();
                    console.error('Permission request error:', error_2);
                    setState(function (prev) { return (__assign(__assign({}, prev), { error: error_2 instanceof Error ? error_2.message : 'Failed to request permission' })); });
                    return [2 /*return*/, false];
                case 3: return [2 /*return*/];
            }
        });
    }); }, [state.isSupported]);
    // Get FCM token
    var getTokenAsync = (0, react_1.useCallback)(function (vapidKey) { return __awaiter(_this, void 0, void 0, function () {
        var token_1, error_3;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    if (!messagingRef.current || state.permission !== 'granted') {
                        return [2 /*return*/, null];
                    }
                    return [4 /*yield*/, (0, messaging_1.getToken)(messagingRef.current, {
                            vapidKey: vapidKey || config.vapidKey,
                        })];
                case 1:
                    token_1 = _a.sent();
                    setState(function (prev) { return (__assign(__assign({}, prev), { token: token_1 })); });
                    return [2 /*return*/, token_1];
                case 2:
                    error_3 = _a.sent();
                    console.error('Token retrieval error:', error_3);
                    setState(function (prev) { return (__assign(__assign({}, prev), { error: error_3 instanceof Error ? error_3.message : 'Failed to get token' })); });
                    return [2 /*return*/, null];
                case 3: return [2 /*return*/];
            }
        });
    }); }, [state.permission, config.vapidKey]);
    // Subscribe to topic
    var subscribeToTopic = (0, react_1.useCallback)(function (topic) { return __awaiter(_this, void 0, void 0, function () {
        var token, _a, response, error_4;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    _b.trys.push([0, 4, , 5]);
                    _a = state.token;
                    if (_a) return [3 /*break*/, 2];
                    return [4 /*yield*/, getTokenAsync()];
                case 1:
                    _a = (_b.sent());
                    _b.label = 2;
                case 2:
                    token = _a;
                    if (!token) {
                        throw new Error('No FCM token available');
                    }
                    return [4 /*yield*/, fetch('/api/fcm/subscribe', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ token: token, topic: topic }),
                        })];
                case 3:
                    response = _b.sent();
                    if (!response.ok) {
                        throw new Error('Failed to subscribe to topic');
                    }
                    return [2 /*return*/, true];
                case 4:
                    error_4 = _b.sent();
                    console.error('Topic subscription error:', error_4);
                    setState(function (prev) { return (__assign(__assign({}, prev), { error: error_4 instanceof Error ? error_4.message : 'Failed to subscribe to topic' })); });
                    return [2 /*return*/, false];
                case 5: return [2 /*return*/];
            }
        });
    }); }, [state.token, getTokenAsync]);
    // Unsubscribe from topic
    var unsubscribeFromTopic = (0, react_1.useCallback)(function (topic) { return __awaiter(_this, void 0, void 0, function () {
        var token, _a, response, error_5;
        return __generator(this, function (_b) {
            switch (_b.label) {
                case 0:
                    _b.trys.push([0, 4, , 5]);
                    _a = state.token;
                    if (_a) return [3 /*break*/, 2];
                    return [4 /*yield*/, getTokenAsync()];
                case 1:
                    _a = (_b.sent());
                    _b.label = 2;
                case 2:
                    token = _a;
                    if (!token) {
                        throw new Error('No FCM token available');
                    }
                    return [4 /*yield*/, fetch('/api/fcm/unsubscribe', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ token: token, topic: topic }),
                        })];
                case 3:
                    response = _b.sent();
                    if (!response.ok) {
                        throw new Error('Failed to unsubscribe from topic');
                    }
                    return [2 /*return*/, true];
                case 4:
                    error_5 = _b.sent();
                    console.error('Topic unsubscription error:', error_5);
                    setState(function (prev) { return (__assign(__assign({}, prev), { error: error_5 instanceof Error ? error_5.message : 'Failed to unsubscribe from topic' })); });
                    return [2 /*return*/, false];
                case 5: return [2 /*return*/];
            }
        });
    }); }, [state.token, getTokenAsync]);
    // Cleanup on unmount
    (0, react_1.useEffect)(function () {
        return function () {
            if (unsubscribeRef.current) {
                unsubscribeRef.current();
            }
        };
    }, []);
    return __assign(__assign({}, state), { initialize: initialize, requestPermission: requestPermission, getToken: getTokenAsync, subscribeToTopic: subscribeToTopic, unsubscribeFromTopic: unsubscribeFromTopic });
}
// ========================================
// NOTIFICATION PERMISSION HOOK
// ========================================
/**
 * Hook for managing notification permissions
 */
function useNotificationPermission() {
    var _this = this;
    var _a = (0, react_1.useState)({
        permission: null,
        isSupported: false,
        isLoading: true,
        error: null,
    }), state = _a[0], setState = _a[1];
    (0, react_1.useEffect)(function () {
        // Check if running in browser
        if (typeof window === 'undefined') {
            setState({
                permission: null,
                isSupported: false,
                isLoading: false,
                error: 'Not supported in server-side environment',
            });
            return;
        }
        // Check if notifications are supported
        if (!('Notification' in window)) {
            setState({
                permission: null,
                isSupported: false,
                isLoading: false,
                error: 'Notifications not supported',
            });
            return;
        }
        setState({
            permission: Notification.permission,
            isSupported: true,
            isLoading: false,
            error: null,
        });
    }, []);
    var requestPermission = (0, react_1.useCallback)(function () { return __awaiter(_this, void 0, void 0, function () {
        var permission_3, error_6;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 2, , 3]);
                    if (!state.isSupported) {
                        throw new Error('Notifications not supported');
                    }
                    setState(function (prev) { return (__assign(__assign({}, prev), { isLoading: true, error: null })); });
                    return [4 /*yield*/, Notification.requestPermission()];
                case 1:
                    permission_3 = _a.sent();
                    setState(function (prev) { return (__assign(__assign({}, prev), { permission: permission_3, isLoading: false })); });
                    return [2 /*return*/, permission_3 === 'granted'];
                case 2:
                    error_6 = _a.sent();
                    setState(function (prev) { return (__assign(__assign({}, prev), { error: error_6 instanceof Error ? error_6.message : 'Failed to request permission', isLoading: false })); });
                    return [2 /*return*/, false];
                case 3: return [2 /*return*/];
            }
        });
    }); }, [state.isSupported]);
    return __assign(__assign({}, state), { requestPermission: requestPermission });
}
// ========================================
// NOTIFICATION PREFERENCES HOOK
// ========================================
/**
 * Hook for managing user notification preferences
 */
function useNotificationPreferences() {
    var _this = this;
    var _a = (0, react_1.useState)(null), preferences = _a[0], setPreferences = _a[1];
    var _b = (0, react_1.useState)(true), isLoading = _b[0], setIsLoading = _b[1];
    var _c = (0, react_1.useState)(null), error = _c[0], setError = _c[1];
    // Load preferences
    var loadPreferences = (0, react_1.useCallback)(function () { return __awaiter(_this, void 0, void 0, function () {
        var response, data, err_1;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 3, 4, 5]);
                    setIsLoading(true);
                    setError(null);
                    return [4 /*yield*/, fetch('/api/fcm/preferences')];
                case 1:
                    response = _a.sent();
                    if (!response.ok) {
                        throw new Error('Failed to load preferences');
                    }
                    return [4 /*yield*/, response.json()];
                case 2:
                    data = _a.sent();
                    setPreferences(data);
                    return [3 /*break*/, 5];
                case 3:
                    err_1 = _a.sent();
                    setError(err_1 instanceof Error ? err_1.message : 'Failed to load preferences');
                    return [3 /*break*/, 5];
                case 4:
                    setIsLoading(false);
                    return [7 /*endfinally*/];
                case 5: return [2 /*return*/];
            }
        });
    }); }, []);
    // Update preferences
    var updatePreferences = (0, react_1.useCallback)(function (updates) { return __awaiter(_this, void 0, void 0, function () {
        var response, updatedPreferences, err_2;
        return __generator(this, function (_a) {
            switch (_a.label) {
                case 0:
                    _a.trys.push([0, 3, , 4]);
                    setError(null);
                    return [4 /*yield*/, fetch('/api/fcm/preferences', {
                            method: 'PATCH',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(updates),
                        })];
                case 1:
                    response = _a.sent();
                    if (!response.ok) {
                        throw new Error('Failed to update preferences');
                    }
                    return [4 /*yield*/, response.json()];
                case 2:
                    updatedPreferences = _a.sent();
                    setPreferences(updatedPreferences);
                    return [2 /*return*/, true];
                case 3:
                    err_2 = _a.sent();
                    setError(err_2 instanceof Error ? err_2.message : 'Failed to update preferences');
                    return [2 /*return*/, false];
                case 4: return [2 /*return*/];
            }
        });
    }); }, []);
    // Load preferences on mount
    (0, react_1.useEffect)(function () {
        loadPreferences();
    }, [loadPreferences]);
    return {
        preferences: preferences,
        isLoading: isLoading,
        error: error,
        loadPreferences: loadPreferences,
        updatePreferences: updatePreferences,
    };
}
// ========================================
// HELPER FUNCTIONS
// ========================================
/**
 * Show a browser notification
 */
function showNotification(notification) {
    if (Notification.permission === 'granted') {
        new Notification(notification.title, {
            body: notification.body,
            icon: notification.icon || config_1.FCM_SETTINGS.DEFAULT_NOTIFICATION_ICON,
            badge: notification.badge || config_1.FCM_SETTINGS.DEFAULT_NOTIFICATION_BADGE,
            tag: notification.tag,
            data: notification.data,
        });
    }
}
//# sourceMappingURL=hooks.js.map