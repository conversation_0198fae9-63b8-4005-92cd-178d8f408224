/**
 * @encreasl/fcm - Shared Firebase Cloud Messaging Package
 * 
 * Provides FCM utilities, configurations, and React hooks
 * for the Encreasl monorepo. Supports both client and server-side usage.
 */

// ========================================
// CORE EXPORTS
// ========================================

// Types
export * from './types';

// Client-side utilities
export * from './client';

// Server-side utilities (for admin apps)
export * from './server';

// Shared utilities
export * from './utils';

// Configuration
export * from './config';

// ========================================
// RE-EXPORTS FOR CONVENIENCE
// ========================================

// Common types that are frequently used
export type {
  FCMConfig,
  FCMMessage,
  NotificationPayload,
  NotificationData,
  NotificationPriority,
  NotificationType,
  FCMDevice,
  FCMTopic,
  NotificationPreferences,
} from './types';

// Common hooks
export {
  useFCM,
  useNotificationPermission,
  useNotificationPreferences,
} from './client/hooks';

// Common utilities
export {
  validateFCMConfig,
  createFCMMessage,
  formatNotificationData,
} from './utils';

// Configuration helpers
export {
  createFCMConfig,
  getFCMConfig,
  validateFCMEnvironment,
} from './config';
