"use strict";
/**
 * Shared FCM Utilities
 *
 * Utility functions that can be used in both client and server environments
 * for Firebase Cloud Messaging operations.
 */
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateFCMConfig = validateFCMConfig;
exports.validateNotificationPayload = validateNotificationPayload;
exports.validateFCMToken = validateFCMToken;
exports.validateTopicName = validateTopicName;
exports.createFCMMessage = createFCMMessage;
exports.formatNotificationData = formatNotificationData;
exports.createWebPushNotification = createWebPushNotification;
exports.createAndroidNotification = createAndroidNotification;
exports.generateNotificationId = generateNotificationId;
exports.isBrowser = isBrowser;
exports.isNotificationSupported = isNotificationSupported;
exports.isServiceWorkerSupported = isServiceWorkerSupported;
exports.getNotificationPermission = getNotificationPermission;
exports.formatNotificationTimestamp = formatNotificationTimestamp;
exports.sanitizeNotificationContent = sanitizeNotificationContent;
exports.createNotificationAnalytics = createNotificationAnalytics;
exports.parseFCMError = parseFCMError;
exports.isRetryableError = isRetryableError;
// ========================================
// VALIDATION UTILITIES
// ========================================
/**
 * Validate FCM configuration
 */
function validateFCMConfig(config) {
    var errors = [];
    var warnings = [];
    // Required fields
    if (!config.apiKey)
        errors.push('API key is required');
    if (!config.authDomain)
        errors.push('Auth domain is required');
    if (!config.projectId)
        errors.push('Project ID is required');
    if (!config.storageBucket)
        errors.push('Storage bucket is required');
    if (!config.messagingSenderId)
        errors.push('Messaging sender ID is required');
    if (!config.appId)
        errors.push('App ID is required');
    // Optional but recommended fields
    if (!config.vapidKey) {
        warnings.push('VAPID key is not configured - web push notifications may not work properly');
    }
    return {
        isValid: errors.length === 0,
        errors: errors,
        warnings: warnings,
    };
}
/**
 * Validate notification payload
 */
function validateNotificationPayload(payload) {
    var errors = [];
    var warnings = [];
    // Required fields
    if (!payload.title)
        errors.push('Notification title is required');
    if (!payload.body)
        errors.push('Notification body is required');
    // Length validations
    if (payload.title && payload.title.length > 100) {
        warnings.push('Notification title is longer than 100 characters and may be truncated');
    }
    if (payload.body && payload.body.length > 200) {
        warnings.push('Notification body is longer than 200 characters and may be truncated');
    }
    return {
        isValid: errors.length === 0,
        errors: errors,
        warnings: warnings,
    };
}
/**
 * Validate FCM token format
 */
function validateFCMToken(token) {
    // Basic FCM token validation
    // FCM tokens are typically 152+ characters long and contain alphanumeric characters, hyphens, and underscores
    return typeof token === 'string' &&
        token.length >= 140 &&
        /^[a-zA-Z0-9_-]+$/.test(token);
}
/**
 * Validate topic name
 */
function validateTopicName(topic) {
    // Topic names must match the pattern: [a-zA-Z0-9-_.~%]+
    return typeof topic === 'string' &&
        topic.length > 0 &&
        topic.length <= 900 &&
        /^[a-zA-Z0-9\-_.~%]+$/.test(topic);
}
// ========================================
// MESSAGE CREATION UTILITIES
// ========================================
/**
 * Create a standardized FCM message
 */
function createFCMMessage(notification, data, options) {
    var message = {
        notification: notification,
    };
    if (data) {
        message.data = data;
    }
    if (options === null || options === void 0 ? void 0 : options.android) {
        message.android = options.android;
    }
    if (options === null || options === void 0 ? void 0 : options.webpush) {
        message.webpush = options.webpush;
    }
    if (options === null || options === void 0 ? void 0 : options.apns) {
        message.apns = options.apns;
    }
    return message;
}
/**
 * Create notification data with proper string conversion
 */
function formatNotificationData(data) {
    var formattedData = {};
    for (var _i = 0, _a = Object.entries(data); _i < _a.length; _i++) {
        var _b = _a[_i], key = _b[0], value = _b[1];
        if (value !== null && value !== undefined) {
            formattedData[key] = String(value);
        }
    }
    return formattedData;
}
/**
 * Create a web push notification with proper formatting
 */
function createWebPushNotification(notification, data, options) {
    return {
        notification: notification,
        data: data,
        webpush: {
            notification: {
                title: notification.title,
                body: notification.body,
                icon: notification.icon,
                badge: notification.badge,
                image: notification.image,
                requireInteraction: options === null || options === void 0 ? void 0 : options.requireInteraction,
                silent: options === null || options === void 0 ? void 0 : options.silent,
                actions: options === null || options === void 0 ? void 0 : options.actions,
            },
            fcmOptions: {
                link: options === null || options === void 0 ? void 0 : options.link,
            },
        },
    };
}
/**
 * Create an Android notification with proper formatting
 */
function createAndroidNotification(notification, data, options) {
    return {
        notification: notification,
        data: data,
        android: {
            priority: (options === null || options === void 0 ? void 0 : options.priority) || "normal",
            notification: {
                title: notification.title,
                body: notification.body,
                icon: notification.icon,
                color: options === null || options === void 0 ? void 0 : options.color,
                sound: options === null || options === void 0 ? void 0 : options.sound,
                channelId: options === null || options === void 0 ? void 0 : options.channelId,
            },
        },
    };
}
// ========================================
// UTILITY FUNCTIONS
// ========================================
/**
 * Generate a unique notification ID
 */
function generateNotificationId() {
    return "notif_".concat(Date.now(), "_").concat(Math.random().toString(36).substr(2, 9));
}
/**
 * Check if code is running in browser environment
 */
function isBrowser() {
    return typeof window !== 'undefined' && typeof navigator !== 'undefined';
}
/**
 * Check if notifications are supported in current environment
 */
function isNotificationSupported() {
    return isBrowser() && 'Notification' in window && 'serviceWorker' in navigator;
}
/**
 * Check if service workers are supported
 */
function isServiceWorkerSupported() {
    return isBrowser() && 'serviceWorker' in navigator;
}
/**
 * Get notification permission status
 */
function getNotificationPermission() {
    if (!isNotificationSupported()) {
        return null;
    }
    return Notification.permission;
}
/**
 * Format timestamp for notifications
 */
function formatNotificationTimestamp(date) {
    if (date === void 0) { date = new Date(); }
    return date.toISOString();
}
/**
 * Sanitize notification content
 */
function sanitizeNotificationContent(content) {
    // Remove HTML tags and limit length
    return content
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .trim()
        .substring(0, 200); // Limit to 200 characters
}
/**
 * Create notification analytics data
 */
function createNotificationAnalytics(notificationId, userId, metadata) {
    return formatNotificationData(__assign({ notificationId: notificationId, userId: userId || 'anonymous', timestamp: formatNotificationTimestamp() }, metadata));
}
// ========================================
// ERROR HANDLING UTILITIES
// ========================================
/**
 * Parse FCM error and provide user-friendly message
 */
function parseFCMError(error) {
    if (typeof error === 'string') {
        return error;
    }
    if (error === null || error === void 0 ? void 0 : error.code) {
        switch (error.code) {
            case 'messaging/invalid-registration-token':
                return 'Invalid or expired notification token';
            case 'messaging/registration-token-not-registered':
                return 'Notification token is not registered';
            case 'messaging/invalid-package-name':
                return 'Invalid package name';
            case 'messaging/invalid-apns-credentials':
                return 'Invalid APNS credentials';
            case 'messaging/too-many-topics':
                return 'Too many topics subscribed';
            case 'messaging/invalid-topic':
                return 'Invalid topic name';
            case 'messaging/topic-name-invalid':
                return 'Topic name is invalid';
            default:
                return error.message || 'Unknown FCM error';
        }
    }
    return (error === null || error === void 0 ? void 0 : error.message) || 'Unknown error occurred';
}
/**
 * Check if error is retryable
 */
function isRetryableError(error) {
    if (!(error === null || error === void 0 ? void 0 : error.code)) {
        return false;
    }
    var retryableCodes = [
        'messaging/server-unavailable',
        'messaging/internal-error',
        'messaging/unknown-error',
    ];
    return retryableCodes.includes(error.code);
}
//# sourceMappingURL=index.js.map