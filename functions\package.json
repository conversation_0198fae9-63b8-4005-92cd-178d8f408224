{"name": "@encreasl/functions", "version": "1.0.0", "description": "Firebase Cloud Functions for Encreasl monorepo", "main": "lib/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "engines": {"node": "18"}, "dependencies": {"firebase-admin": "^12.0.0", "firebase-functions": "^5.0.0", "cors": "^2.8.5", "express": "^4.18.2", "zod": "^3.22.4", "node-cron": "^3.0.3", "handlebars": "^4.7.8", "nodemailer": "^6.9.8"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/node": "^20.10.6", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "eslint": "^8.56.0", "firebase-functions-test": "^3.1.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}, "private": true}