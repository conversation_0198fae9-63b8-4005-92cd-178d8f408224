{"version": 3, "file": "newsletter-notifications.js", "sourceRoot": "", "sources": ["../../src/triggers/newsletter-notifications.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,+DAAoE;AACpE,8CAA+C;AAC/C,yDAAqD;AACrD,wDAAwD;AAExD,MAAM,UAAU,GAAG,IAAI,wBAAU,EAAE,CAAC;AACpC,MAAM,EAAE,GAAG,IAAA,wBAAY,GAAE,CAAC;AAE1B,2CAA2C;AAC3C,kCAAkC;AAClC,2CAA2C;AAE9B,QAAA,sBAAsB,GAAG,IAAA,6BAAiB,EACrD;IACE,QAAQ,EAAE,6BAA6B;IACvC,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,EAAE;CACnB,EACD,KAAK,EAAE,KAAK,EAAE,EAAE;;IACd,IAAI,CAAC;QACH,MAAM,gBAAgB,GAAG,MAAA,KAAK,CAAC,IAAI,0CAAE,IAAI,EAAE,CAAC;QAC5C,MAAM,cAAc,GAAG,KAAK,CAAC,MAAM,CAAC,cAAc,CAAC;QAEnD,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,WAAM,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAC3C,OAAO;QACT,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,uCAAuC,cAAc,EAAE,EAAE,gBAAgB,CAAC,CAAC;QAEvF,2CAA2C;QAC3C,0CAA0C;QAC1C,2CAA2C;QAE3C,yDAAyD;QACzD,MAAM,cAAc,GAAG,MAAM,EAAE;aAC5B,UAAU,CAAC,0BAA0B,CAAC;aACtC,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,gBAAgB,CAAC,KAAK,CAAC;aAC5C,KAAK,CAAC,CAAC,CAAC;aACR,GAAG,EAAE,CAAC;QAET,IAAI,CAAC,cAAc,CAAC,KAAK,EAAE,CAAC;YAC1B,MAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAEhD,IAAI,CAAA,MAAA,SAAS,CAAC,WAAW,0CAAE,UAAU,KAAI,CAAA,MAAA,SAAS,CAAC,SAAS,0CAAE,MAAM,IAAG,CAAC,EAAE,CAAC;gBACzE,MAAM,mBAAmB,GAAG;oBAC1B,YAAY,EAAE;wBACZ,KAAK,EAAE,oCAAoC;wBAC3C,IAAI,EAAE,mFAAmF;wBACzF,IAAI,EAAE,oCAAoC;wBAC1C,KAAK,EAAE,uCAAuC;wBAC9C,WAAW,EAAE,qBAAqB;qBACnC;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,oBAAoB;wBAC1B,cAAc;wBACd,KAAK,EAAE,gBAAgB,CAAC,KAAK;wBAC7B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;qBACjC;iBACF,CAAC;gBAEF,MAAM,aAAa,GAAG,MAAM,UAAU,CAAC,YAAY,CACjD,SAAS,CAAC,SAAS,EACnB,mBAAmB,EACnB,QAAQ,CACT,CAAC;gBAEF,WAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,aAAa,CAAC,CAAC;gBAE3D,qCAAqC;gBACrC,MAAM,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;gBAErE,uCAAuC;gBACvC,IAAI,MAAA,gBAAgB,CAAC,WAAW,0CAAE,SAAS,EAAE,CAAC;oBAC5C,MAAM,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;gBACtE,CAAC;gBACD,IAAI,MAAA,gBAAgB,CAAC,WAAW,0CAAE,OAAO,EAAE,CAAC;oBAC1C,MAAM,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,SAAS,EAAE,iBAAiB,CAAC,CAAC;gBAC5E,CAAC;YACH,CAAC;QACH,CAAC;QAED,2CAA2C;QAC3C,wBAAwB;QACxB,2CAA2C;QAE3C,oDAAoD;QACpD,MAAM,kBAAkB,GAAG,MAAM,EAAE;aAChC,UAAU,CAAC,0BAA0B,CAAC;aACtC,KAAK,CAAC,wBAAwB,EAAE,IAAI,EAAE,IAAI,CAAC;aAC3C,KAAK,CAAC,QAAQ,EAAE,gBAAgB,EAAE,gBAAgB,CAAC;aACnD,GAAG,EAAE,CAAC;QAET,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACpC,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;YACzB,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClD,eAAe,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;YAC3C,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,qBAAqB,GAAG;gBAC5B,YAAY,EAAE;oBACZ,KAAK,EAAE,2BAA2B;oBAClC,IAAI,EAAE,GAAG,gBAAgB,CAAC,KAAK,oCAAoC;oBACnE,IAAI,EAAE,2BAA2B;oBACjC,WAAW,EAAE,+BAA+B;iBAC7C;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,mBAAmB;oBACzB,cAAc;oBACd,KAAK,EAAE,gBAAgB,CAAC,KAAK;oBAC7B,MAAM,EAAE,gBAAgB,CAAC,MAAM,IAAI,SAAS;oBAC5C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;iBACjC;aACF,CAAC;YAEF,MAAM,eAAe,GAAG,MAAM,UAAU,CAAC,YAAY,CACnD,eAAe,EACf,qBAAqB,EACrB,KAAK,CACN,CAAC;YAEF,WAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE,eAAe,CAAC,CAAC;QACtE,CAAC;QAED,2CAA2C;QAC3C,0BAA0B;QAC1B,2CAA2C;QAE3C,8CAA8C;QAC9C,MAAM,gBAAgB,GAAG,MAAM,EAAE;aAC9B,UAAU,CAAC,YAAY,CAAC;aACxB,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,QAAQ,CAAC;aAC/B,GAAG,EAAE,CAAC;QAET,MAAM,eAAe,GAAG,gBAAgB,CAAC,IAAI,CAAC;QAC9C,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEvE,IAAI,UAAU,CAAC,QAAQ,CAAC,eAAe,CAAC,EAAE,CAAC;YACzC,4CAA4C;YAC5C,MAAM,qBAAqB,GAAG;gBAC5B,YAAY,EAAE;oBACZ,KAAK,EAAE,kCAAkC;oBACzC,IAAI,EAAE,gCAAgC,eAAe,CAAC,cAAc,EAAE,0BAA0B;oBAChG,IAAI,EAAE,2BAA2B;oBACjC,KAAK,EAAE,mCAAmC;oBAC1C,WAAW,EAAE,6BAA6B;iBAC3C;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,sBAAsB;oBAC5B,SAAS,EAAE,eAAe,CAAC,QAAQ,EAAE;oBACrC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;oBAChC,kBAAkB,EAAE,MAAM;iBAC3B;aACF,CAAC;YAEF,MAAM,eAAe,GAAG,MAAM,UAAU,CAAC,WAAW,CAClD,OAAO,EACP,qBAAqB,EACrB,MAAM,CACP,CAAC;YAEF,WAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,eAAe,CAAC,CAAC;YAE/D,4BAA4B;YAC5B,MAAM,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC;gBACpC,IAAI,EAAE,wBAAwB;gBAC9B,KAAK,EAAE,eAAe;gBACtB,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,gBAAgB,EAAE,IAAI;aACvB,CAAC,CAAC;QACL,CAAC;QAED,2CAA2C;QAC3C,8BAA8B;QAC9B,2CAA2C;QAE3C,wEAAwE;QACxE,IAAI,gBAAgB,CAAC,WAAW,EAAE,CAAC;YACjC,MAAM,SAAS,GAAG,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,MAAM,CAChE,GAAG,CAAC,EAAE,CAAC,gBAAgB,CAAC,WAAW,CAAC,GAAG,CAAC,KAAK,IAAI,CAClD,CAAC;YAEF,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACzB,4CAA4C;gBAC5C,MAAM,0BAA0B,GAAG;oBACjC,YAAY,EAAE;wBACZ,KAAK,EAAE,iCAAiC;wBACxC,IAAI,EAAE,8BAA8B,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,0CAA0C;wBAClG,IAAI,EAAE,gCAAgC;wBACtC,WAAW,EAAE,sCAAsC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE;qBACzE;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,wBAAwB;wBAC9B,cAAc;wBACd,SAAS,EAAE,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC;wBAC9B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;qBACjC;iBACF,CAAC;gBAEF,+EAA+E;gBAC/E,UAAU,CAAC,KAAK,IAAI,EAAE;;oBACpB,IAAI,cAAc,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACnC,MAAM,SAAS,GAAG,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;wBAChD,IAAI,CAAA,MAAA,SAAS,CAAC,SAAS,0CAAE,MAAM,IAAG,CAAC,EAAE,CAAC;4BACpC,MAAM,UAAU,CAAC,YAAY,CAC3B,SAAS,CAAC,SAAS,EACnB,0BAA0B,EAC1B,KAAK,CACN,CAAC;wBACJ,CAAC;oBACH,CAAC;gBACH,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,kBAAkB;YACvC,CAAC;QACH,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,qDAAqD,cAAc,EAAE,CAAC,CAAC;IAErF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,WAAM,CAAC,KAAK,CAAC,wDAAwD,EAAE,KAAK,CAAC,CAAC;QAC9E,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CACF,CAAC"}