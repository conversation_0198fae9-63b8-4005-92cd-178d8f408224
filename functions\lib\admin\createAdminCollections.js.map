{"version": 3, "file": "createAdminCollections.js", "sourceRoot": "", "sources": ["../../src/admin/createAdminCollections.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWH,wDAsBC;AA8JD,4CA8JC;AA3VD,sDAAwC;AACxC,wDAAiE;AAEjE,uBAAuB;AACvB,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;AAE7B;;GAEG;AACI,KAAK,UAAU,sBAAsB;IAC1C,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,yCAAyC,CAAC,CAAC;QAEvD,4CAA4C;QAC5C,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;QAEzB,6BAA6B;QAC7B,MAAM,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAEhC,6BAA6B;QAC7B,MAAM,wBAAwB,CAAC,KAAK,CAAC,CAAC;QAEtC,mBAAmB;QACnB,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;QAErB,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAChE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;QAC1D,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,kBAAkB,CAAC,KAAiC;IACjE,MAAM,eAAe,GAAG,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC;IAErD,mBAAmB;IACnB,MAAM,cAAc,GAAG;QACrB,EAAE,EAAE,aAAa;QACjB,IAAI,EAAE,aAAa;QACnB,WAAW,EAAE,aAAa;QAC1B,WAAW,EAAE,qDAAqD;QAClE,WAAW,EAAE,CAAC,GAAG,CAAC,EAAE,+BAA+B;QACnD,cAAc,EAAE,EAAE;QAClB,KAAK,EAAE,EAAE;QACT,aAAa,EAAE,CAAC,OAAO,EAAE,iBAAiB,EAAE,QAAQ,CAAC;QACrD,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE,KAAK;QACnB,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;QAC1B,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;QAC1B,SAAS,EAAE,QAAQ;QACnB,QAAQ,EAAE,IAAI;KACf,CAAC;IAEF,aAAa;IACb,MAAM,SAAS,GAAG;QAChB,EAAE,EAAE,OAAO;QACX,IAAI,EAAE,OAAO;QACb,WAAW,EAAE,eAAe;QAC5B,WAAW,EAAE,2CAA2C;QACxD,WAAW,EAAE;YACX,YAAY,EAAE,cAAc,EAAE,YAAY;YAC1C,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,iBAAiB;YACnE,YAAY,EAAE,cAAc,EAAE,cAAc;YAC5C,eAAe;SAChB;QACD,cAAc,EAAE,EAAE;QAClB,KAAK,EAAE,CAAC;QACR,aAAa,EAAE,yBAAyB;QACxC,aAAa,EAAE,CAAC,iBAAiB,EAAE,QAAQ,CAAC;QAC5C,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE,KAAK;QACnB,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;QAC1B,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;QAC1B,SAAS,EAAE,QAAQ;QACnB,QAAQ,EAAE,IAAI;KACf,CAAC;IAEF,uBAAuB;IACvB,MAAM,kBAAkB,GAAG;QACzB,EAAE,EAAE,iBAAiB;QACrB,IAAI,EAAE,iBAAiB;QACvB,WAAW,EAAE,iBAAiB;QAC9B,WAAW,EAAE,kCAAkC;QAC/C,WAAW,EAAE;YACX,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,iBAAiB;YACnE,YAAY,EAAE,cAAc,EAAE,cAAc;YAC5C,gBAAgB;SACjB;QACD,cAAc,EAAE,EAAE;QAClB,KAAK,EAAE,CAAC;QACR,aAAa,EAAE,mBAAmB;QAClC,aAAa,EAAE,CAAC,QAAQ,CAAC;QACzB,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE,KAAK;QACnB,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;QAC1B,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;QAC1B,SAAS,EAAE,QAAQ;QACnB,QAAQ,EAAE,IAAI;KACf,CAAC;IAEF,cAAc;IACd,MAAM,UAAU,GAAG;QACjB,EAAE,EAAE,QAAQ;QACZ,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,QAAQ;QACrB,WAAW,EAAE,gDAAgD;QAC7D,WAAW,EAAE;YACX,cAAc,EAAE,gBAAgB,EAAE,cAAc;YAChD,YAAY,EAAE,cAAc;SAC7B;QACD,cAAc,EAAE,EAAE;QAClB,KAAK,EAAE,CAAC;QACR,aAAa,EAAE,6BAA6B;QAC5C,aAAa,EAAE,EAAE;QACjB,YAAY,EAAE,IAAI;QAClB,YAAY,EAAE,KAAK;QACnB,SAAS,EAAE,CAAC;QACZ,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;QAC1B,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;QAC1B,SAAS,EAAE,QAAQ;QACnB,QAAQ,EAAE,IAAI;KACf,CAAC;IAEF,qBAAqB;IACrB,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,cAAc,CAAC,CAAC;IAC9D,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,CAAC;IACnD,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE,kBAAkB,CAAC,CAAC;IACtE,KAAK,CAAC,GAAG,CAAC,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,UAAU,CAAC,CAAC;IAErD,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;AAC7C,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,wBAAwB,CAAC,KAAiC;IACvE,MAAM,qBAAqB,GAAG,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC;IAEjE,+BAA+B;IAC/B,MAAM,UAAU,GAAG,CAAC,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC;IAE1E,8BAA8B;IAC9B,MAAM,eAAe,GAAG;QACtB,OAAO,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC;QACpE,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,CAAC;QACvD,KAAK,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,CAAC;QAC3D,QAAQ,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,qBAAqB,EAAE,gBAAgB,CAAC;QACnE,SAAS,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,sBAAsB,CAAC;KACtD,CAAC;IAEF,kDAAkD;IAClD,KAAK,MAAM,QAAQ,IAAI,UAAU,EAAE,CAAC;QAClC,MAAM,OAAO,GAAG,eAAe,CAAC,QAAwC,CAAC,CAAC;QAE1E,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE,CAAC;YAC7B,MAAM,YAAY,GAAG,GAAG,QAAQ,IAAI,MAAM,EAAE,CAAC;YAC7C,MAAM,UAAU,GAAG;gBACjB,EAAE,EAAE,YAAY;gBAChB,IAAI,EAAE,YAAY;gBAClB,WAAW,EAAE,GAAG,qBAAqB,CAAC,MAAM,CAAC,IAAI,qBAAqB,CAAC,QAAQ,CAAC,EAAE;gBAClF,WAAW,EAAE,OAAO,MAAM,IAAI,QAAQ,EAAE;gBACxC,QAAQ;gBACR,QAAQ,EAAE,QAAQ;gBAClB,MAAM;gBACN,kBAAkB,EAAE,IAAI;gBACxB,QAAQ,EAAE,IAAI;gBACd,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;gBAC1B,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;gBAC1B,SAAS,EAAE,CAAC;gBACZ,SAAS,EAAE,CAAC;aACb,CAAC;YAEF,KAAK,CAAC,GAAG,CAAC,qBAAqB,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,UAAU,CAAC,CAAC;QACjE,CAAC;IACH,CAAC;IAED,OAAO,CAAC,GAAG,CAAC,mCAAmC,CAAC,CAAC;AACnD,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,gBAAgB,CAAC,KAAa,EAAE,GAAW,EAAE,WAAmB;IACpF,IAAI,CAAC;QACH,OAAO,CAAC,GAAG,CAAC,8BAA8B,KAAK,EAAE,CAAC,CAAC;QAEnD,+BAA+B;QAC/B,MAAM,YAAY,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QACvE,IAAI,YAAY,CAAC,MAAM,EAAE,CAAC;YACxB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;YACzC,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;QACtD,CAAC;QAED,6BAA6B;QAC7B,MAAM,SAAS,GAAG;YAChB,EAAE,EAAE,GAAG;YACP,KAAK;YACL,WAAW;YACX,YAAY,EAAE,OAAO;YACrB,aAAa,EAAE,IAAI;YAEnB,mBAAmB;YACnB,OAAO,EAAE,yBAAyB;YAClC,QAAQ,EAAE,aAAa;YACvB,WAAW,EAAE,CAAC,GAAG,CAAC;YAClB,QAAQ,EAAE,IAAI;YACd,YAAY,EAAE,IAAI;YAElB,sBAAsB;YACtB,SAAS,EAAE,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YACpC,QAAQ,EAAE,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC;YACnD,KAAK,EAAE,qBAAqB;YAE5B,iDAAiD;YACjD,cAAc,EAAE;gBACd,OAAO,EAAE;oBACP,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,IAAI;oBACf,UAAU,EAAE,IAAI;oBAChB,WAAW,EAAE,IAAI;iBAClB;gBACD,KAAK,EAAE;oBACL,SAAS,EAAE,IAAI;oBACf,SAAS,EAAE,IAAI;oBACf,WAAW,EAAE,IAAI;oBACjB,aAAa,EAAE,GAAG,EAAE,QAAQ;iBAC7B;gBACD,KAAK,EAAE;oBACL,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,IAAI;oBACf,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,IAAI;oBACf,cAAc,EAAE,IAAI;iBACrB;gBACD,SAAS,EAAE;oBACT,OAAO,EAAE,IAAI;oBACb,SAAS,EAAE,IAAI;oBACf,sBAAsB,EAAE,IAAI;iBAC7B;gBACD,QAAQ,EAAE;oBACR,aAAa,EAAE,IAAI;oBACnB,aAAa,EAAE,IAAI;oBACnB,qBAAqB,EAAE,IAAI;oBAC3B,gBAAgB,EAAE,IAAI;iBACvB;aACF;YAED,uBAAuB;YACvB,mBAAmB,EAAE;gBACnB,iBAAiB,EAAE,IAAI;gBACvB,gBAAgB,EAAE,IAAI;gBACtB,cAAc,EAAE,IAAI;gBACpB,kBAAkB,EAAE,IAAI;gBACxB,aAAa,EAAE,CAAC,EAAE,gBAAgB;aACnC;YAED,iBAAiB;YACjB,kBAAkB,EAAE;gBAClB,UAAU,EAAE,KAAK,EAAE,4BAA4B;gBAC/C,cAAc,EAAE,EAAE,EAAE,aAAa;gBACjC,qBAAqB,EAAE,CAAC;aACzB;YAED,oBAAoB;YACpB,UAAU,EAAE,CAAC;YACb,mBAAmB,EAAE,CAAC;YAEtB,cAAc;YACd,WAAW,EAAE;gBACX,KAAK,EAAE,OAAO;gBACd,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,YAAY;gBACxB,UAAU,EAAE,KAAK;gBACjB,eAAe,EAAE,MAAM;gBACvB,oBAAoB,EAAE,IAAI;gBAC1B,kBAAkB,EAAE,IAAI;aACzB;YAED,WAAW;YACX,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;YAC1B,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;YAC1B,SAAS,EAAE,QAAQ;YACnB,SAAS,EAAE,QAAQ;YACnB,OAAO,EAAE,CAAC;YAEV,cAAc;YACd,SAAS,EAAE,KAAK;SACjB,CAAC;QAEF,oBAAoB;QACpB,MAAM,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QAE3D,yBAAyB;QACzB,MAAM,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,MAAM,CAAC;YAC3D,SAAS,EAAE,sBAAU,CAAC,SAAS,CAAC,CAAC,CAAC;YAClC,UAAU,EAAE,qBAAS,CAAC,GAAG,EAAE;SAC5B,CAAC,CAAC;QAEH,2BAA2B;QAC3B,MAAM,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;YACpD,MAAM,EAAE,GAAG;YACX,KAAK,EAAE,OAAO;YACd,QAAQ,EAAE,IAAI;YACd,QAAQ,EAAE,KAAK;YACf,UAAU,EAAE,YAAY;YACxB,UAAU,EAAE,KAAK;YACjB,eAAe,EAAE,MAAM;YACvB,oBAAoB,EAAE,IAAI;YAC1B,kBAAkB,EAAE,IAAI;YACxB,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;YAC1B,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;SAC3B,CAAC,CAAC;QAEH,qBAAqB;QACrB,MAAM,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC;YAC1C,QAAQ,EAAE,oBAAoB;YAC9B,UAAU,EAAE,QAAQ;YACpB,SAAS,EAAE,QAAQ;YACnB,MAAM,EAAE,aAAa;YACrB,QAAQ,EAAE,aAAa;YACvB,UAAU,EAAE,GAAG;YACf,WAAW,EAAE,6BAA6B,KAAK,EAAE;YACjD,SAAS,EAAE,SAAS;YACpB,SAAS,EAAE,QAAQ;YACnB,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;YAC1B,QAAQ,EAAE,MAAM;YAChB,QAAQ,EAAE,gBAAgB;YAC1B,OAAO,EAAE,IAAI;YACb,cAAc,EAAE,CAAC,OAAO,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,CAAC;YACzD,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SACnD,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,0CAA0C,KAAK,EAAE,CAAC,CAAC;QAC/D,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;IACxC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,6CAA6C;AAC7C,SAAS,qBAAqB,CAAC,MAAc;IAC3C,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC7E,CAAC"}