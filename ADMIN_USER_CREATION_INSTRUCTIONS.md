# Create John Admin User - Manual Instructions

## Issue
The Firebase Admin SDK service account doesn't have sufficient permissions to create users programmatically. This is a common security restriction.

## Solution: Create User Manually + Run Script

### Step 1: Create User in Firebase Console

1. **Open Firebase Console:**
   - Go to: https://console.firebase.google.com/project/encreasl-daa43/authentication/users

2. **Add New User:**
   - Click the "Add user" button
   - Enter the following details:
     - **Email:** `<EMAIL>`
     - **Password:** `@Iamachessgrandmaster23`
   - Click "Add user"

3. **Verify User Creation:**
   - The user should appear in the Authentication > Users list
   - Note down the User UID (you'll see it in the list)

### Step 2: Run the Admin Setup Script

After creating the user manually, run the script to set up admin permissions:

```bash
cd scripts
node create-john-admin.js
```

This script will:
- Find the existing user by email
- Create the admin user document in Firestore
- Set custom claims for admin permissions
- Update role user count
- Create admin preferences

### Step 3: Verify Admin User Setup

1. **Check Firestore Collections:**
   - `admin_users` collection should have a document with the user's UID
   - `admin_roles` collection should show updated user count

2. **Check Custom Claims:**
   - In Firebase Console > Authentication > Users
   - Click on the user to see custom claims

## Alternative: Fix Permissions (For Future)

To avoid manual user creation in the future, grant the service account required permissions:

1. **Go to Google Cloud Console IAM:**
   - Visit: https://console.developers.google.com/iam-admin/iam/project?project=encreasl-daa43

2. **Find Service Account:**
   - Look for: `<EMAIL>`

3. **Add Roles:**
   - Edit the service account
   - Add role: `Service Usage Consumer`
   - Add role: `Firebase Admin SDK Administrator Service Agent`

4. **Wait for Propagation:**
   - Changes take 5-10 minutes to take effect

## User Details

- **Email:** <EMAIL>
- **Password:** @Iamachessgrandmaster23
- **Role:** Administrator
- **Permissions:** Full admin access (content management, user management, etc.)
- **Collections:** Will be created in `admin_users`, `admin_roles`, `admin_preferences`

## Troubleshooting

If the script fails:
1. Ensure the user was created manually in Firebase Console
2. Check that the email matches exactly: `<EMAIL>`
3. Verify Firebase project ID is correct: `encreasl-daa43`
4. Check that `.env.local` file has correct Firebase Admin credentials
