/**
 * Create John Admin User in admin_users Collection
 * 
 * This script creates the admin user in the correct admin_users collection
 * that the admin authentication system expects.
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables from the root .env.local file
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Admin user details
const ADMIN_EMAIL = '<EMAIL>';
const FIREBASE_AUTH_UID = 'T6W1Urk96RaYBItw3ecjdlQbvoH3'; // From Firebase Console
const ADMIN_NAME = 'John Admin';

// Firebase Admin configuration
const serviceAccount = {
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
};

async function createAdminUser() {
  try {
    console.log('🚀 Creating John Admin User in admin_users collection...');
    console.log('📧 Email:', ADMIN_EMAIL);
    console.log('🆔 Firebase Auth UID:', FIREBASE_AUTH_UID);
    console.log('📊 Project ID:', serviceAccount.projectId);

    // Initialize Firebase Admin
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.projectId,
      });
    }

    const db = admin.firestore();
    const auth = admin.auth();

    // Step 1: Check if admin role exists
    console.log('\n1️⃣  Checking admin role...');
    const adminRoleDoc = await db.collection('admin_roles').doc('admin').get();
    if (!adminRoleDoc.exists) {
      console.log('   ❌ Admin role not found in admin_roles collection');
      console.log('   📝 Please ensure the admin role is created first');
      return;
    }
    
    const adminRoleData = adminRoleDoc.data();
    console.log('   ✅ Admin role found');
    console.log(`   👑 Role name: ${adminRoleData.displayName}`);
    console.log(`   📊 Level: ${adminRoleData.level}`);

    // Step 2: Create admin user document in admin_users collection
    console.log('\n2️⃣  Creating admin user document...');
    
    const currentTime = admin.firestore.Timestamp.now();
    
    const adminUserDocument = {
      // === IDENTITY ===
      id: FIREBASE_AUTH_UID,
      email: ADMIN_EMAIL,
      displayName: ADMIN_NAME,
      
      // === AUTHENTICATION ===
      authProvider: 'email',
      emailVerified: true,
      
      // === AUTHORIZATION ===
      roleRef: 'admin_roles/admin',
      roleName: adminRoleData.displayName || 'Administrator',
      permissions: adminRoleData.permissions || ['*'],
      isActive: true,
      isSuperAdmin: false,
      
      // === PROFILE ===
      firstName: 'John',
      lastName: 'Admin',
      title: 'Administrator',
      department: 'Administration',
      
      // === CMS CAPABILITIES ===
      cmsPermissions: {
        content: {
          canCreate: true,
          canEdit: true,
          canDelete: true,
          canPublish: true,
          canSchedule: true,
        },
        media: {
          canUpload: true,
          canDelete: true,
          canOrganize: true,
          maxUploadSize: 100, // 100MB
        },
        users: {
          canView: true,
          canCreate: true,
          canEdit: true,
          canDelete: true,
          canChangeRoles: true,
        },
        analytics: {
          canView: true,
          canExport: true,
          canConfigureDashboards: true,
        },
        settings: {
          canViewSystem: true,
          canEditSystem: true,
          canManageIntegrations: true,
          canManageBackups: true,
        },
      },
      
      // === WORKFLOW PERMISSIONS ===
      workflowPermissions: {
        canApproveContent: true,
        canRejectContent: true,
        canAssignTasks: true,
        canCreateWorkflows: true,
        approvalLevel: 4, // High level
      },
      
      // === ACCESS RESTRICTIONS ===
      accessRestrictions: {
        requireMFA: false,
        sessionTimeout: 60,
        maxConcurrentSessions: 3,
      },
      
      // === ACTIVITY TRACKING ===
      loginCount: 0,
      failedLoginAttempts: 0,
      lastLoginAt: null,
      lastActiveAt: null,
      
      // === PREFERENCES ===
      preferences: {
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        dashboardLayout: 'grid',
        notificationsEnabled: true,
        emailNotifications: true,
      },
      
      // === METADATA ===
      createdAt: currentTime,
      updatedAt: currentTime,
      createdBy: 'system',
      updatedBy: 'system',
      version: 1,
      
      // === SOFT DELETE ===
      isDeleted: false,
    };

    // Check if admin user document already exists
    const existingAdminDoc = await db.collection('admin_users').doc(FIREBASE_AUTH_UID).get();
    
    if (existingAdminDoc.exists) {
      console.log('   ⚠️  Admin user document already exists, updating...');
      await db.collection('admin_users').doc(FIREBASE_AUTH_UID).update({
        ...adminUserDocument,
        updatedAt: currentTime,
        version: admin.firestore.FieldValue.increment(1)
      });
      console.log('   ✅ Admin user document updated');
    } else {
      await db.collection('admin_users').doc(FIREBASE_AUTH_UID).set(adminUserDocument);
      console.log('   ✅ Admin user document created');
    }

    // Step 3: Try to set custom claims
    console.log('\n3️⃣  Setting custom claims...');
    try {
      await auth.setCustomUserClaims(FIREBASE_AUTH_UID, {
        admin: true,
        role: 'admin',
        permissions: adminRoleData.permissions || ["*"],
        userId: FIREBASE_AUTH_UID
      });
      console.log('   ✅ Custom claims set successfully');
    } catch (claimsError) {
      console.log('   ⚠️  Could not set custom claims (permission issue)');
      console.log('   📝 You can set these manually in Firebase Console if needed');
    }

    // Step 4: Update role user count
    console.log('\n4️⃣  Updating role user count...');
    await db.collection('admin_roles').doc('admin').update({
      userCount: admin.firestore.FieldValue.increment(1),
      lastUsedAt: currentTime
    });
    console.log('   ✅ Role user count updated');

    // Step 5: Create admin preferences document
    console.log('\n5️⃣  Creating admin preferences...');
    const preferencesDoc = await db.collection('admin_preferences').doc(FIREBASE_AUTH_UID).get();
    
    if (!preferencesDoc.exists) {
      await db.collection('admin_preferences').doc(FIREBASE_AUTH_UID).set({
        userId: FIREBASE_AUTH_UID,
        theme: 'light',
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        dashboardLayout: 'grid',
        notificationsEnabled: true,
        emailNotifications: true,
        createdAt: currentTime,
        updatedAt: currentTime
      });
      console.log('   ✅ Admin preferences created');
    } else {
      console.log('   ✅ Admin preferences already exist');
    }

    // Success summary
    console.log('\n🎉 SUCCESS! John Admin User Created in Admin System');
    console.log('=====================================================');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log(`🆔 Firebase Auth UID: ${FIREBASE_AUTH_UID}`);
    console.log(`🆔 Admin Document ID: ${FIREBASE_AUTH_UID}`);
    console.log(`👑 Role: admin`);
    console.log(`🔐 Permissions: ${adminRoleData.permissions?.join(', ') || '*'}`);
    console.log(`✅ Active: true`);
    
    console.log('\n📝 Ready for Admin Login!');
    console.log('The user can now log in to the admin panel with:');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log('🔒 Password: @Iamachessgrandmaster23');
    
    console.log('\n🔗 Collections Created:');
    console.log('✅ admin_users/' + FIREBASE_AUTH_UID);
    console.log('✅ admin_preferences/' + FIREBASE_AUTH_UID);
    console.log('✅ admin_roles/admin (updated)');

  } catch (error) {
    console.error('\n❌ Error creating admin user:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  createAdminUser()
    .then(() => {
      console.log('\n🏁 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createAdminUser };
