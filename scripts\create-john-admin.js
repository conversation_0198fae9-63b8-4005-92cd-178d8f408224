/**
 * Create John Admin User Script
 * 
 * This script creates the specific admin user requested:
 * Email: <EMAIL>
 * Password: @Iamachessgrandmaster23
 * Role: Admin
 * 
 * Prerequisites:
 * 1. User must already exist in Firebase Auth (created manually)
 * 2. Admin role must exist in the roles collection
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables from the root .env.local file
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Admin user details
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = '@Iamachessgrandmaster23';
const ADMIN_NAME = 'John Admin';

// Firebase Admin configuration
const serviceAccount = {
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
};

async function createJohnAdmin() {
  try {
    console.log('🚀 Starting John Admin User Creation...');
    console.log('📧 Email:', ADMIN_EMAIL);
    console.log('📊 Project ID:', serviceAccount.projectId);

    // Initialize Firebase Admin
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.projectId,
      });
    }

    const db = admin.firestore();
    const auth = admin.auth();

    // Step 1: Try to create user in Firebase Auth (or check if exists)
    console.log('\n1️⃣  Checking/Creating Firebase Auth user...');
    let userRecord;
    let authUid;

    try {
      // First try to get existing user
      userRecord = await auth.getUserByEmail(ADMIN_EMAIL);
      console.log('   ✅ User already exists in Firebase Auth');
      console.log(`   🆔 UID: ${userRecord.uid}`);
      authUid = userRecord.uid;
    } catch (error) {
      if (error.code === 'auth/user-not-found') {
        console.log('   ⚠️  User not found, attempting to create...');
        try {
          // Try to create the user
          userRecord = await auth.createUser({
            email: ADMIN_EMAIL,
            password: ADMIN_PASSWORD,
            displayName: ADMIN_NAME,
            emailVerified: true,
          });
          console.log('   ✅ User created in Firebase Auth');
          console.log(`   🆔 UID: ${userRecord.uid}`);
          authUid = userRecord.uid;
        } catch (createError) {
          console.log('   ❌ Failed to create user in Firebase Auth');
          console.log('   📝 Please create the user manually in Firebase Console:');
          console.log('   🔗 https://console.firebase.google.com/project/encreasl-daa43/authentication/users');
          console.log(`   📧 Email: ${ADMIN_EMAIL}`);
          console.log(`   🔒 Password: ${ADMIN_PASSWORD}`);
          console.log(`   ⚠️  Error: ${createError.message}`);

          // Continue with Firestore operations anyway
          authUid = null;
        }
      } else {
        console.log('   ❌ Error checking Firebase Auth:', error.message);
        authUid = null;
      }
    }

    // Generate a consistent user ID based on email
    const userId = authUid || `john-admin-${ADMIN_EMAIL.replace(/[^a-zA-Z0-9]/g, '-')}`;

    // Step 2: Check if admin role exists
    console.log('\n2️⃣  Checking admin role...');
    const adminRoleDoc = await db.collection('roles').doc('admin').get();
    if (!adminRoleDoc.exists) {
      console.log('   ❌ Admin role not found in roles collection');
      console.log('   📝 Please ensure the admin role is created first');
      return;
    }
    
    const adminRoleData = adminRoleDoc.data();
    console.log('   ✅ Admin role found');
    console.log(`   👑 Role name: ${adminRoleData.name}`);
    console.log(`   📊 Level: ${adminRoleData.level}`);

    // Step 3: Create user document in users collection
    console.log('\n3️⃣  Creating user document...');

    const currentTime = admin.firestore.Timestamp.now();
    
    const userDocument = {
      // Basic user info
      accountType: "premium",
      authProvider: "email",
      bio: "Administrator user for Encreasl platform",
      createdAt: currentTime,
      createdBy: "system",
      displayName: ADMIN_NAME,
      email: ADMIN_EMAIL,
      emailVerified: true,
      failedLoginAttempts: 0,
      firstName: "John",
      id: userId,
      isActive: true,
      isDeleted: false,
      isVerified: true,
      lastActiveAt: null,
      lastLoginAt: null,
      lastName: "Admin",
      loginCount: 0,
      phoneVerified: false,
      updatedAt: currentTime,
      updatedBy: "system",
      version: 1,
      
      // Admin-specific fields
      role: "admin",
      permissions: adminRoleData.permissions || ["*"],
      
      // Nested objects (maps)
      analytics: {
        firstVisitAt: currentTime,
        signupSource: "admin_creation",
        totalSessions: 0,
        totalTimeSpent: 0
      },
      
      compliance: {
        ccpaCompliant: true,
        gdprCompliant: true,
        privacyPolicyAcceptedAt: currentTime,
        termsAcceptedAt: currentTime
      },
      
      preferences: {
        currency: "USD",
        dateFormat: "MM/DD/YYYY",
        language: "en",
        theme: "light",
        timeFormat: "12h",
        timezone: "UTC",
        
        notifications: {
          email: true,
          marketing: false,
          push: true,
          sms: false
        },
        
        privacy: {
          allowSearchByEmail: false,
          profileVisibility: "private",
          showEmail: false,
          showPhone: false
        }
      }
    };

    // Check if user document already exists
    const existingUserQuery = await db.collection('users')
      .where('email', '==', ADMIN_EMAIL)
      .limit(1)
      .get();
    
    if (!existingUserQuery.empty) {
      console.log('   ⚠️  User document already exists, updating...');
      const existingDoc = existingUserQuery.docs[0];
      await existingDoc.ref.update({
        ...userDocument,
        updatedAt: currentTime,
        version: admin.firestore.FieldValue.increment(1)
      });
      console.log('   ✅ User document updated');
    } else {
      await db.collection('users').doc(userId).set(userDocument);
      console.log('   ✅ User document created');
    }

    // Step 4: Set custom claims if we have auth UID
    console.log('\n4️⃣  Setting custom claims...');
    if (authUid) {
      try {
        await auth.setCustomUserClaims(authUid, {
          admin: true,
          role: 'admin',
          permissions: adminRoleData.permissions || ["*"],
          userId: userId
        });
        console.log('   ✅ Custom claims set successfully');
      } catch (claimsError) {
        console.log('   ⚠️  Failed to set custom claims:', claimsError.message);
        console.log('   📝 You can set custom claims manually in Firebase Console');
        console.log('   🔗 https://console.firebase.google.com/project/encreasl-daa43/authentication/users');
      }
    } else {
      console.log('   ⚠️  Skipping custom claims (no Firebase Auth UID available)');
      console.log('   📝 Custom claims can be set manually in Firebase Console');
      console.log('   🔗 https://console.firebase.google.com/project/encreasl-daa43/authentication/users');
    }

    // Step 5: Update role user count
    console.log('\n5️⃣  Updating role user count...');
    await db.collection('roles').doc('admin').update({
      userCount: admin.firestore.FieldValue.increment(1),
      updatedAt: currentTime
    });
    console.log('   ✅ Role user count updated');

    // Success summary
    console.log('\n🎉 SUCCESS! John Admin User Created');
    console.log('=====================================');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log(`🆔 Firebase Auth UID: ${authUid || 'Not available'}`);
    console.log(`🆔 Firestore User ID: ${userId}`);
    console.log(`👑 Role: admin`);
    console.log(`🔐 Permissions: ${adminRoleData.permissions?.join(', ') || '*'}`);
    console.log(`✅ Active: true`);
    
    console.log('\n📝 Next Steps:');
    console.log('1. Test login with the admin credentials');
    console.log('2. Verify admin permissions in your application');
    console.log('3. Check Firestore console to confirm data');
    
    console.log('\n🔗 Firestore Console:');
    console.log(`https://console.firebase.google.com/project/${serviceAccount.projectId}/firestore/data`);

  } catch (error) {
    console.error('\n❌ Error creating John admin user:', error);
    
    if (error.code === 'permission-denied') {
      console.log('\n🔒 Permission Error:');
      console.log('   - Check Firebase Admin SDK credentials');
      console.log('   - Verify service account has proper permissions');
      console.log('   - Ensure Firestore rules allow admin operations');
    }
    
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  createJohnAdmin()
    .then(() => {
      console.log('\n🏁 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { createJohnAdmin };
