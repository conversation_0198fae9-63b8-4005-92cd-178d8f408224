"use strict";
/**
 * @encreasl/fcm - Shared Firebase Cloud Messaging Package
 *
 * Provides FCM utilities, configurations, and React hooks
 * for the Encreasl monorepo. Supports both client and server-side usage.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateFCMEnvironment = exports.getFCMConfig = exports.createFCMConfig = exports.formatNotificationData = exports.createFCMMessage = exports.validateFCMConfig = exports.useNotificationPreferences = exports.useNotificationPermission = exports.useFCM = void 0;
// ========================================
// CORE EXPORTS
// ========================================
// Types
__exportStar(require("./types"), exports);
// Client-side utilities
__exportStar(require("./client"), exports);
// Server-side utilities (for admin apps)
__exportStar(require("./server"), exports);
// Shared utilities
__exportStar(require("./utils"), exports);
// Configuration
__exportStar(require("./config"), exports);
// Common hooks
var hooks_1 = require("./client/hooks");
Object.defineProperty(exports, "useFCM", { enumerable: true, get: function () { return hooks_1.useFCM; } });
Object.defineProperty(exports, "useNotificationPermission", { enumerable: true, get: function () { return hooks_1.useNotificationPermission; } });
Object.defineProperty(exports, "useNotificationPreferences", { enumerable: true, get: function () { return hooks_1.useNotificationPreferences; } });
// Common utilities
var utils_1 = require("./utils");
Object.defineProperty(exports, "validateFCMConfig", { enumerable: true, get: function () { return utils_1.validateFCMConfig; } });
Object.defineProperty(exports, "createFCMMessage", { enumerable: true, get: function () { return utils_1.createFCMMessage; } });
Object.defineProperty(exports, "formatNotificationData", { enumerable: true, get: function () { return utils_1.formatNotificationData; } });
// Configuration helpers
var config_1 = require("./config");
Object.defineProperty(exports, "createFCMConfig", { enumerable: true, get: function () { return config_1.createFCMConfig; } });
Object.defineProperty(exports, "getFCMConfig", { enumerable: true, get: function () { return config_1.getFCMConfig; } });
Object.defineProperty(exports, "validateFCMEnvironment", { enumerable: true, get: function () { return config_1.validateFCMEnvironment; } });
//# sourceMappingURL=index.js.map