'use client';

import { AuthGuard } from './AuthGuard';
import { useAdminOperations } from '@/hooks/useAuth';
import { AlertTriangle } from 'lucide-react';

interface AdminRouteGuardProps {
  children: React.ReactNode;
  requiredPermission?: string;
  requireSuperAdmin?: boolean;
  fallback?: React.ReactNode;
}

// Insufficient permissions component
function InsufficientPermissionsScreen({ 
  requiredPermission, 
  requireSuperAdmin 
}: { 
  requiredPermission?: string;
  requireSuperAdmin?: boolean;
}) {
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center max-w-md">
        <div className="inline-flex items-center justify-center w-16 h-16 bg-yellow-100 rounded-2xl mb-6">
          <AlertTriangle className="w-8 h-8 text-yellow-600" />
        </div>
        <h1 className="text-2xl font-bold text-gray-900 mb-4">Insufficient Permissions</h1>
        <p className="text-gray-600 mb-6">
          {requireSuperAdmin 
            ? 'This action requires super admin privileges.'
            : requiredPermission 
              ? `You need the "${requiredPermission}" permission to access this area.`
              : 'You don\'t have the required permissions to access this area.'
          }
        </p>
        <button
          onClick={() => window.history.back()}
          className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          Go Back
        </button>
      </div>
    </div>
  );
}

// Admin route guard component
export function AdminRouteGuard({ 
  children, 
  requiredPermission,
  requireSuperAdmin = false,
  fallback 
}: AdminRouteGuardProps) {
  return (
    <AuthGuard fallback={fallback}>
      <PermissionCheck 
        requiredPermission={requiredPermission}
        requireSuperAdmin={requireSuperAdmin}
      >
        {children}
      </PermissionCheck>
    </AuthGuard>
  );
}

// Permission check component (used within AuthGuard)
function PermissionCheck({ 
  children, 
  requiredPermission,
  requireSuperAdmin 
}: {
  children: React.ReactNode;
  requiredPermission?: string;
  requireSuperAdmin?: boolean;
}) {
  const { hasPermission, isSuperAdmin } = useAdminOperations();

  // Check super admin requirement
  if (requireSuperAdmin && !isSuperAdmin) {
    return (
      <InsufficientPermissionsScreen 
        requireSuperAdmin={true}
      />
    );
  }

  // Check specific permission requirement
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return (
      <InsufficientPermissionsScreen 
        requiredPermission={requiredPermission}
      />
    );
  }

  return <>{children}</>;
}

// Higher-order component for protecting admin routes with permissions
export function withAdminRouteGuard<P extends object>(
  Component: React.ComponentType<P>,
  options?: {
    requiredPermission?: string;
    requireSuperAdmin?: boolean;
    fallback?: React.ReactNode;
  }
) {
  const WrappedComponent = (props: P) => {
    return (
      <AdminRouteGuard 
        requiredPermission={options?.requiredPermission}
        requireSuperAdmin={options?.requireSuperAdmin}
        fallback={options?.fallback}
      >
        <Component {...props} />
      </AdminRouteGuard>
    );
  };

  WrappedComponent.displayName = `withAdminRouteGuard(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

export default AdminRouteGuard;
