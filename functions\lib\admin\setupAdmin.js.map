{"version": 3, "file": "setupAdmin.js", "sourceRoot": "", "sources": ["../../src/admin/setupAdmin.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,uDAAiE;AACjE,8CAA+C;AAC/C,sDAAwC;AACxC,qEAAoF;AAEpF,MAAM,EAAE,GAAG,KAAK,CAAC,SAAS,EAAE,CAAC;AAC7B,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;AAE1B;;;GAGG;AACU,QAAA,qBAAqB,GAAG,IAAA,cAAM,EACzC;IACE,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,MAAM;IACd,cAAc,EAAE,GAAG,EAAE,YAAY;CAClC,EACD,KAAK,EAAE,OAAO,EAAE,EAAE;IAChB,IAAI,CAAC;QACH,WAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QAEvD,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,cAAc,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;QAE7E,+BAA+B;QAC/B,IAAI,CAAC,eAAe,IAAI,CAAC,kBAAkB,IAAI,CAAC,cAAc,EAAE,CAAC;YAC/D,MAAM,IAAI,kBAAU,CAClB,kBAAkB,EAClB,8EAA8E,CAC/E,CAAC;QACJ,CAAC;QAED,+CAA+C;QAC/C,MAAM,aAAa,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC;QACxE,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;YACzB,WAAM,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YAChD,OAAO;gBACL,OAAO,EAAE,IAAI;gBACb,OAAO,EAAE,kCAAkC;gBAC3C,aAAa,EAAE,IAAI;aACpB,CAAC;QACJ,CAAC;QAED,6CAA6C;QAC7C,WAAM,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;QACvD,MAAM,IAAA,+CAAsB,GAAE,CAAC;QAE/B,mDAAmD;QACnD,WAAM,CAAC,IAAI,CAAC,+CAA+C,CAAC,CAAC;QAC7D,IAAI,aAAqB,CAAC;QAE1B,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC;gBACvC,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE,kBAAkB;gBAC5B,WAAW,EAAE,cAAc;gBAC3B,aAAa,EAAE,IAAI;aACpB,CAAC,CAAC;YAEH,aAAa,GAAG,UAAU,CAAC,GAAG,CAAC;YAE/B,oCAAoC;YACpC,MAAM,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE;gBAC5C,KAAK,EAAE,IAAI;gBACX,UAAU,EAAE,IAAI;gBAChB,IAAI,EAAE,aAAa;gBACnB,WAAW,EAAE,CAAC,GAAG,CAAC;aACnB,CAAC,CAAC;YAEH,WAAM,CAAC,IAAI,CAAC,yCAAyC,eAAe,EAAE,CAAC,CAAC;QAC1E,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,2BAA2B,EAAE,CAAC;gBAC/C,qCAAqC;gBACrC,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC;gBAChE,aAAa,GAAG,YAAY,CAAC,GAAG,CAAC;gBAEjC,uBAAuB;gBACvB,MAAM,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE;oBAC5C,KAAK,EAAE,IAAI;oBACX,UAAU,EAAE,IAAI;oBAChB,IAAI,EAAE,aAAa;oBACnB,WAAW,EAAE,CAAC,GAAG,CAAC;iBACnB,CAAC,CAAC;gBAEH,WAAM,CAAC,IAAI,CAAC,sCAAsC,eAAe,EAAE,CAAC,CAAC;YACvE,CAAC;iBAAM,CAAC;gBACN,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,+CAA+C;QAC/C,WAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QACzD,MAAM,IAAA,yCAAgB,EAAC,eAAe,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;QAEvE,mCAAmC;QACnC,MAAM,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC;YAC1C,QAAQ,EAAE,eAAe,aAAa,EAAE;YACxC,UAAU,EAAE,eAAe;YAC3B,SAAS,EAAE,cAAc;YACzB,MAAM,EAAE,mBAAmB;YAC3B,QAAQ,EAAE,cAAc;YACxB,WAAW,EAAE,uCAAuC;YACpD,SAAS,EAAE,SAAS;YACpB,SAAS,EAAE,cAAc;YACzB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE;YAC1C,QAAQ,EAAE,MAAM;YAChB,QAAQ,EAAE,QAAQ;YAClB,OAAO,EAAE,IAAI;YACb,cAAc,EAAE,CAAC,QAAQ,EAAE,YAAY,EAAE,OAAO,EAAE,OAAO,CAAC;YAC1D,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;SACnD,CAAC,CAAC;QAEH,+CAA+C;QAC/C,MAAM,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,GAAG,CAAC;YAC3D,WAAW,EAAE,IAAI;YACjB,aAAa,EAAE,KAAK,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,EAAE;YAC9C,aAAa,EAAE,aAAa;YAC5B,OAAO,EAAE,OAAO;YAChB,QAAQ,EAAE;gBACR,eAAe,EAAE,IAAI;gBACrB,YAAY,EAAE,IAAI;gBAClB,iBAAiB,EAAE,IAAI;gBACvB,qBAAqB,EAAE,IAAI;gBAC3B,eAAe,EAAE,KAAK,EAAE,uBAAuB;aAChD;YACD,QAAQ,EAAE;gBACR,cAAc,EAAE,EAAE,EAAE,UAAU;gBAC9B,eAAe,EAAE,CAAC;gBAClB,eAAe,EAAE,EAAE,EAAE,UAAU;gBAC/B,cAAc,EAAE;oBACd,SAAS,EAAE,CAAC;oBACZ,gBAAgB,EAAE,IAAI;oBACtB,gBAAgB,EAAE,IAAI;oBACtB,cAAc,EAAE,IAAI;oBACpB,mBAAmB,EAAE,IAAI;iBAC1B;aACF;SACF,CAAC,CAAC;QAEH,WAAM,CAAC,IAAI,CAAC,oDAAoD,CAAC,CAAC;QAElE,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,uCAAuC;YAChD,YAAY,EAAE,aAAa;YAC3B,eAAe;YACf,QAAQ,EAAE;gBACR,2BAA2B;gBAC3B,wBAAwB;gBACxB,eAAe;gBACf,oBAAoB;gBACpB,iBAAiB;gBACjB,gCAAgC;aACjC;SACF,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,WAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAExD,IAAI,KAAK,YAAY,kBAAU,EAAE,CAAC;YAChC,MAAM,KAAK,CAAC;QACd,CAAC;QAED,MAAM,IAAI,kBAAU,CAAC,UAAU,EAAE,mCAAmC,CAAC,CAAC;IACxE,CAAC;AACH,CAAC,CACF,CAAC;AAEF;;GAEG;AACU,QAAA,sBAAsB,GAAG,IAAA,cAAM,EAC1C;IACE,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,EAAE;CACnB,EACD,KAAK,EAAE,OAAO,EAAE,EAAE;IAChB,IAAI,CAAC;QACH,gCAAgC;QAChC,MAAM,eAAe,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,GAAG,EAAE,CAAC;QAEvF,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC;YAC5B,OAAO;gBACL,WAAW,EAAE,KAAK;gBAClB,OAAO,EAAE,8BAA8B;aACxC,CAAC;QACJ,CAAC;QAED,MAAM,YAAY,GAAG,eAAe,CAAC,IAAI,EAAE,CAAC;QAE5C,sCAAsC;QACtC,MAAM,CAAC,aAAa,EAAE,mBAAmB,EAAE,aAAa,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC5E,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;YAC3C,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;YACjD,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE;SAC5C,CAAC,CAAC;QAEH,MAAM,gBAAgB,GAAG,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC,mBAAmB,CAAC,KAAK,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;QAEpG,wBAAwB;QACxB,MAAM,CAAC,SAAS,EAAE,eAAe,EAAE,SAAS,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAChE,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;YACxF,EAAE,CAAC,UAAU,CAAC,mBAAmB,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;YAC9F,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC;SACzF,CAAC,CAAC;QAEH,OAAO;YACL,WAAW,EAAE,CAAA,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,WAAW,KAAI,gBAAgB;YAC1D,aAAa,EAAE,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,aAAa;YAC1C,OAAO,EAAE,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,OAAO;YAC9B,QAAQ,EAAE,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,QAAQ;YAChC,UAAU,EAAE;gBACV,UAAU,EAAE,SAAS;gBACrB,gBAAgB,EAAE,eAAe;gBACjC,eAAe,EAAE,SAAS;aAC3B;YACD,WAAW,EAAE;gBACX,UAAU,EAAE,CAAC,aAAa,CAAC,KAAK;gBAChC,gBAAgB,EAAE,CAAC,mBAAmB,CAAC,KAAK;gBAC5C,UAAU,EAAE,CAAC,aAAa,CAAC,KAAK;aACjC;SACF,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,WAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,KAAK,CAAC,CAAC;QAC3D,MAAM,IAAI,kBAAU,CAAC,UAAU,EAAE,qCAAqC,CAAC,CAAC;IAC1E,CAAC;AACH,CAAC,CACF,CAAC;AAEF;;GAEG;AACU,QAAA,gBAAgB,GAAG,IAAA,cAAM,EACpC;IACE,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,MAAM;IACd,cAAc,EAAE,GAAG;CACpB,EACD,KAAK,EAAE,OAAO,EAAE,EAAE;;IAChB,IAAI,CAAC;QACH,+BAA+B;QAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;YAClB,MAAM,IAAI,kBAAU,CAAC,iBAAiB,EAAE,uBAAuB,CAAC,CAAC;QACnE,CAAC;QAED,MAAM,SAAS,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QAEjF,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC;YACtB,MAAM,IAAI,kBAAU,CAAC,mBAAmB,EAAE,6BAA6B,CAAC,CAAC;QAC3E,CAAC;QAED,MAAM,UAAU,GAAG,SAAS,CAAC,IAAI,EAAE,CAAC;QAEpC,IAAI,CAAC,CAAA,UAAU,aAAV,UAAU,uBAAV,UAAU,CAAE,YAAY,CAAA,EAAE,CAAC;YAC9B,MAAM,IAAI,kBAAU,CAAC,mBAAmB,EAAE,wCAAwC,CAAC,CAAC;QACtF,CAAC;QAED,MAAM,EAAE,gBAAgB,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;QAE1C,IAAI,gBAAgB,KAAK,4BAA4B,EAAE,CAAC;YACtD,MAAM,IAAI,kBAAU,CAAC,kBAAkB,EAAE,2BAA2B,CAAC,CAAC;QACxE,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,mCAAmC,UAAU,CAAC,KAAK,EAAE,CAAC,CAAC;QAEnE,mDAAmD;QACnD,MAAM,WAAW,GAAG;YAClB,aAAa;YACb,mBAAmB;YACnB,gBAAgB;YAChB,kBAAkB;YAClB,mBAAmB;SACpB,CAAC;QAEF,KAAK,MAAM,cAAc,IAAI,WAAW,EAAE,CAAC;YACzC,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,cAAc,CAAC,CAAC,GAAG,EAAE,CAAC;YAC3D,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;YAEzB,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;gBAC1B,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACxB,CAAC,CAAC,CAAC;YAEH,iFAAiF;YACjF,IAAI,CAAC,CAAA,MAAC,KAAa,CAAC,IAAI,0CAAE,MAAM,CAAA;gBAAE,SAAS;YAC3C,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;QACvB,CAAC;QAED,6CAA6C;QAC7C,MAAM,kBAAkB,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,GAAG,EAAE,CAAC;QACpE,MAAM,SAAS,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;QAE7B,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACpC,IAAI,GAAG,CAAC,EAAE,KAAK,OAAO,CAAC,IAAK,CAAC,GAAG,EAAE,CAAC;gBACjC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YAC5B,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,CAAA,MAAC,SAAiB,CAAC,IAAI,0CAAE,MAAM,IAAG,CAAC,EAAE,CAAC;YACxC,MAAM,SAAS,CAAC,MAAM,EAAE,CAAC;QAC3B,CAAC;QAED,sBAAsB;QACtB,MAAM,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC,MAAM,EAAE,CAAC;QAElE,WAAM,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;QAE5C,OAAO;YACL,OAAO,EAAE,IAAI;YACb,OAAO,EAAE,iCAAiC;YAC1C,OAAO,EAAE,UAAU,CAAC,KAAK;YACzB,OAAO,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SAClC,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,WAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QAErD,IAAI,KAAK,YAAY,kBAAU,EAAE,CAAC;YAChC,MAAM,KAAK,CAAC;QACd,CAAC;QAED,MAAM,IAAI,kBAAU,CAAC,UAAU,EAAE,8BAA8B,CAAC,CAAC;IACnE,CAAC;AACH,CAAC,CACF,CAAC"}