# 🚀 Admin System Setup Script

This script will programmatically create the admin collections and users in your Firebase project.

## 📋 What This Script Does

1. **Creates Admin Roles**:
   - Super Admin (Level 10)
   - Administrator (Level 8) 
   - Content Manager (Level 6)
   - Editor (Level 4)

2. **Creates Admin Permissions**:
   - 25+ granular permissions for content, media, users, settings, analytics

3. **Creates Super Admin User**:
   - Email: `<EMAIL>`
   - Password: `AdminPassword123!`
   - Full system access

4. **Creates Firestore Collections**:
   - `admin_users`
   - `admin_roles`
   - `admin_permissions`
   - `admin_preferences`
   - `admin_audit_logs`
   - `system_config`

## 🏃‍♂️ How to Run

### Step 1: Navigate to Scripts Directory
```bash
cd scripts
```

### Step 2: Install Dependencies
```bash
npm install
```

### Step 3: Run the Setup Script
```bash
npm run setup-admin
```

## 📊 Expected Output

```
🚀 Starting admin system initialization...
📊 Project ID: encreasl-daa43
🔧 Creating admin roles...
✅ Created role: Super Admin
✅ Created role: Administrator
✅ Created role: Content Manager
✅ Created role: Editor
🔧 Creating admin permissions...
✅ Created permission: View Content
✅ Created permission: Create Content
... (25+ permissions)
🔧 Creating super admin user...
✅ Created Firebase Auth user: <EMAIL>
✅ Created admin user document: <EMAIL>
✅ Super admin user created successfully!
🎉 Admin system initialization completed successfully!

📋 Summary:
✅ Created 4 admin roles
✅ Created 25+ permissions
✅ Created super admin user
✅ Created system configuration

🔐 Login Credentials:
📧 Email: <EMAIL>
🔑 Password: AdminPassword123!

🔍 Check your Firebase Console:
- Firestore Database should now have admin collections
- Authentication should have the admin user
```

## 🔍 Verification

After running the script, check your Firebase Console:

### Firestore Database
You should see these collections:
- `admin_users` (1 document)
- `admin_roles` (4 documents)
- `admin_permissions` (25+ documents)
- `admin_preferences` (1 document)
- `admin_audit_logs` (1 document)
- `system_config` (1 document)

### Authentication
You should see:
- User: `<EMAIL>`
- Custom claims: `admin: true, superAdmin: true`

## 🔐 Login Information

**Email**: `<EMAIL>`  
**Password**: `AdminPassword123!`

> ⚠️ **Important**: Change this password after first login!

## 🛠️ Troubleshooting

### Error: "Permission denied"
- Make sure your Firebase project has Firestore enabled
- Check that your service account has the correct permissions

### Error: "Email already exists"
- The script will use the existing user if the email already exists
- It will update the custom claims and create the Firestore document

### Error: "Project not found"
- Verify your project ID in the script matches your Firebase project
- Check your service account credentials

## 🔄 Re-running the Script

The script is idempotent - you can run it multiple times safely:
- Existing roles and permissions will be updated
- Existing users will be updated with new data
- No duplicate data will be created

## 📞 Support

If you encounter any issues:
1. Check the Firebase Console for error messages
2. Verify your internet connection
3. Ensure Firebase project is properly configured
4. Check that Firestore and Authentication are enabled

## 🎯 Next Steps

After successful setup:
1. Login to your admin panel with the provided credentials
2. Change the default password
3. Create additional admin users as needed
4. Configure role permissions as required
5. Test the admin functionality
