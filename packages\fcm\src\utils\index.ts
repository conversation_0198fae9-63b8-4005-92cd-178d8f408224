/**
 * Shared FCM Utilities
 * 
 * Utility functions that can be used in both client and server environments
 * for Firebase Cloud Messaging operations.
 */

import type { 
  FCMConfig, 
  FCMMessage, 
  NotificationPayload, 
  NotificationData,
  FCMValidationResult 
} from '../types';

// ========================================
// VALIDATION UTILITIES
// ========================================

/**
 * Validate FCM configuration
 */
export function validateFCMConfig(config: Partial<FCMConfig>): FCMValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields
  if (!config.apiKey) errors.push('API key is required');
  if (!config.authDomain) errors.push('Auth domain is required');
  if (!config.projectId) errors.push('Project ID is required');
  if (!config.storageBucket) errors.push('Storage bucket is required');
  if (!config.messagingSenderId) errors.push('Messaging sender ID is required');
  if (!config.appId) errors.push('App ID is required');

  // Optional but recommended fields
  if (!config.vapidKey) {
    warnings.push('VAPID key is not configured - web push notifications may not work properly');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validate notification payload
 */
export function validateNotificationPayload(payload: Partial<NotificationPayload>): FCMValidationResult {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required fields
  if (!payload.title) errors.push('Notification title is required');
  if (!payload.body) errors.push('Notification body is required');

  // Length validations
  if (payload.title && payload.title.length > 100) {
    warnings.push('Notification title is longer than 100 characters and may be truncated');
  }
  if (payload.body && payload.body.length > 200) {
    warnings.push('Notification body is longer than 200 characters and may be truncated');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validate FCM token format
 */
export function validateFCMToken(token: string): boolean {
  // Basic FCM token validation
  // FCM tokens are typically 152+ characters long and contain alphanumeric characters, hyphens, and underscores
  return typeof token === 'string' && 
         token.length >= 140 && 
         /^[a-zA-Z0-9_-]+$/.test(token);
}

/**
 * Validate topic name
 */
export function validateTopicName(topic: string): boolean {
  // Topic names must match the pattern: [a-zA-Z0-9-_.~%]+
  return typeof topic === 'string' && 
         topic.length > 0 && 
         topic.length <= 900 && 
         /^[a-zA-Z0-9\-_.~%]+$/.test(topic);
}

// ========================================
// MESSAGE CREATION UTILITIES
// ========================================

/**
 * Create a standardized FCM message
 */
export function createFCMMessage(
  notification: NotificationPayload,
  data?: NotificationData,
  options?: {
    android?: any;
    webpush?: any;
    apns?: any;
  }
): FCMMessage {
  const message: FCMMessage = {
    notification,
  };

  if (data) {
    message.data = data;
  }

  if (options?.android) {
    message.android = options.android;
  }

  if (options?.webpush) {
    message.webpush = options.webpush;
  }

  if (options?.apns) {
    message.apns = options.apns;
  }

  return message;
}

/**
 * Create notification data with proper string conversion
 */
export function formatNotificationData(data: Record<string, any>): NotificationData {
  const formattedData: NotificationData = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (value !== null && value !== undefined) {
      formattedData[key] = String(value);
    }
  }
  
  return formattedData;
}

/**
 * Create a web push notification with proper formatting
 */
export function createWebPushNotification(
  notification: NotificationPayload,
  data?: NotificationData,
  options?: {
    requireInteraction?: boolean;
    silent?: boolean;
    actions?: Array<{ action: string; title: string; icon?: string }>;
    link?: string;
  }
): FCMMessage {
  return {
    notification,
    data,
    webpush: {
      notification: {
        title: notification.title,
        body: notification.body,
        icon: notification.icon,
        badge: notification.badge,
        image: notification.image,
        requireInteraction: options?.requireInteraction,
        silent: options?.silent,
        actions: options?.actions,
      },
      fcmOptions: {
        link: options?.link,
      },
    },
  };
}

/**
 * Create an Android notification with proper formatting
 */
export function createAndroidNotification(
  notification: NotificationPayload,
  data?: NotificationData,
  options?: {
    priority?: "normal" | "high";
    channelId?: string;
    sound?: string;
    color?: string;
  }
): FCMMessage {
  return {
    notification,
    data,
    android: {
      priority: options?.priority || "normal",
      notification: {
        title: notification.title,
        body: notification.body,
        icon: notification.icon,
        color: options?.color,
        sound: options?.sound,
        channelId: options?.channelId,
      },
    },
  };
}

// ========================================
// UTILITY FUNCTIONS
// ========================================

/**
 * Generate a unique notification ID
 */
export function generateNotificationId(): string {
  return `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Check if code is running in browser environment
 */
export function isBrowser(): boolean {
  return typeof window !== 'undefined' && typeof navigator !== 'undefined';
}

/**
 * Check if notifications are supported in current environment
 */
export function isNotificationSupported(): boolean {
  return isBrowser() && 'Notification' in window && 'serviceWorker' in navigator;
}

/**
 * Check if service workers are supported
 */
export function isServiceWorkerSupported(): boolean {
  return isBrowser() && 'serviceWorker' in navigator;
}

/**
 * Get notification permission status
 */
export function getNotificationPermission(): NotificationPermission | null {
  if (!isNotificationSupported()) {
    return null;
  }
  return Notification.permission;
}

/**
 * Format timestamp for notifications
 */
export function formatNotificationTimestamp(date: Date = new Date()): string {
  return date.toISOString();
}

/**
 * Sanitize notification content
 */
export function sanitizeNotificationContent(content: string): string {
  // Remove HTML tags and limit length
  return content
    .replace(/<[^>]*>/g, '') // Remove HTML tags
    .trim()
    .substring(0, 200); // Limit to 200 characters
}

/**
 * Create notification analytics data
 */
export function createNotificationAnalytics(
  notificationId: string,
  userId?: string,
  metadata?: Record<string, any>
): NotificationData {
  return formatNotificationData({
    notificationId,
    userId: userId || 'anonymous',
    timestamp: formatNotificationTimestamp(),
    ...metadata,
  });
}

// ========================================
// ERROR HANDLING UTILITIES
// ========================================

/**
 * Parse FCM error and provide user-friendly message
 */
export function parseFCMError(error: any): string {
  if (typeof error === 'string') {
    return error;
  }

  if (error?.code) {
    switch (error.code) {
      case 'messaging/invalid-registration-token':
        return 'Invalid or expired notification token';
      case 'messaging/registration-token-not-registered':
        return 'Notification token is not registered';
      case 'messaging/invalid-package-name':
        return 'Invalid package name';
      case 'messaging/invalid-apns-credentials':
        return 'Invalid APNS credentials';
      case 'messaging/too-many-topics':
        return 'Too many topics subscribed';
      case 'messaging/invalid-topic':
        return 'Invalid topic name';
      case 'messaging/topic-name-invalid':
        return 'Topic name is invalid';
      default:
        return error.message || 'Unknown FCM error';
    }
  }

  return error?.message || 'Unknown error occurred';
}

/**
 * Check if error is retryable
 */
export function isRetryableError(error: any): boolean {
  if (!error?.code) {
    return false;
  }

  const retryableCodes = [
    'messaging/server-unavailable',
    'messaging/internal-error',
    'messaging/unknown-error',
  ];

  return retryableCodes.includes(error.code);
}
