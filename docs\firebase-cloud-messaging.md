# 🔔 Firebase Cloud Messaging (FCM) Implementation Guide

This document provides a comprehensive guide to the **complete** Firebase Cloud Messaging implementation in the Encreasl monorepo.

## 📋 Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Setup & Configuration](#setup--configuration)
- [Monorepo Integration](#monorepo-integration)
- [Shared FCM Package](#shared-fcm-package)
- [Cloud Functions](#cloud-functions)
- [Client Integration](#client-integration)
- [Service Workers](#service-workers)
- [API Routes](#api-routes)
- [Components](#components)
- [Testing](#testing)
- [Deployment](#deployment)
- [Troubleshooting](#troubleshooting)

## 🎯 Overview

Our **complete** FCM implementation provides:

### ✅ **Fully Implemented Features**

- **🏗️ Professional Monorepo Architecture**: Proper Turborepo integration with shared packages
- **📦 Shared FCM Package** (`@encreasl/fcm`): Reusable utilities, types, and configurations
- **⚡ Complete Cloud Functions Suite**: All 13+ functions implemented and working
- **🔧 HTTP API Endpoints**: Custom notification sending, token management, analytics
- **📅 Scheduled Functions**: Daily digests, weekly reports, cleanup tasks
- **🚀 PubSub Processing**: Bulk notifications, retries, analytics processing
- **📞 Callable Functions**: Client-side topic management and preferences
- **🎯 Campaign Notifications**: Marketing campaign status and activation alerts
- **👥 User Lifecycle**: Welcome, onboarding, and milestone notifications
- **📧 Contact & Newsletter**: Form submissions and subscription notifications
- **🔐 Admin Notifications**: Priority-based admin alerts and reports
- **📱 Service Workers**: Complete templates for both web and admin apps
- **🎨 React Hooks**: Ready-to-use hooks for FCM integration
- **🛡️ Type Safety**: Comprehensive TypeScript types and validation
- **📊 Analytics**: Notification tracking and performance monitoring

### 🏗️ **Professional Architecture**

```
encreasl/
├── functions/                           # 🔥 Firebase Cloud Functions
│   ├── src/
│   │   ├── triggers/
│   │   │   ├── contact-notifications.ts      ✅ Contact form alerts
│   │   │   ├── newsletter-notifications.ts   ✅ Newsletter subscriptions
│   │   │   ├── user-notifications.ts         ✅ User lifecycle events
│   │   │   ├── user-auth-triggers.ts         ✅ Authentication events
│   │   │   ├── campaign-notifications.ts     ✅ Campaign management
│   │   │   ├── scheduled-notifications.ts    ✅ Daily/weekly reports
│   │   │   ├── http-notifications.ts         ✅ HTTP API endpoints
│   │   │   ├── pubsub-notifications.ts       ✅ Bulk processing
│   │   │   └── callable-functions.ts         ✅ Client callable functions
│   │   ├── services/
│   │   │   └── fcm-service.ts               ✅ Complete FCM service
│   │   └── types/
│   │       └── notifications.ts             ✅ Comprehensive types
│   └── package.json                         ✅ Monorepo integrated
├── packages/
│   ├── fcm/                            # 📦 Shared FCM Package
│   │   ├── src/
│   │   │   ├── client/                      ✅ Client-side utilities
│   │   │   ├── server/                      ✅ Server-side utilities
│   │   │   ├── types/                       ✅ Shared types
│   │   │   ├── config/                      ✅ Configuration utilities
│   │   │   └── utils/                       ✅ Common utilities
│   │   └── package.json                     ✅ Proper exports
│   └── auth/                           # 🔐 Authentication (FCM integrated)
├── apps/
│   ├── web-admin/                      # 👨‍💼 Admin Dashboard
│   │   └── public/
│   │       └── firebase-messaging-sw.js     ✅ Admin service worker
│   └── web/                            # 🌐 Marketing Site (FCM ready)
├── templates/
│   └── firebase-messaging-sw-web.js    # 📄 Web app SW template
├── turbo.json                          # ⚡ Turborepo config (updated)
├── pnpm-workspace.yaml                 # 📦 Workspace config (updated)
└── package.json                        # 🎯 Root scripts (updated)
```

## 🚀 **Monorepo Integration**

### **Turborepo Configuration**

Our FCM implementation is fully integrated into the Turborepo build system:

```json
// turbo.json
{
  "tasks": {
    "build:functions": {
      "dependsOn": ["^build"],
      "outputs": ["lib/**"]
    },
    "deploy:functions": {
      "dependsOn": ["build:functions", "lint:functions", "type-check:functions"]
    }
  }
}
```

### **Package Scripts**

```json
// package.json (root)
{
  "scripts": {
    "build:functions": "cd functions && npm run build",
    "deploy:functions": "cd functions && npm run deploy",
    "dev:functions": "cd functions && npm run build:watch",
    "lint:functions": "cd functions && npm run lint",
    "test:functions": "cd functions && npm run test"
  }
}
```

### **Workspace Integration**

```yaml
# pnpm-workspace.yaml
packages:
  - "apps/*"
  - "packages/*"
  - "functions"        # ✅ Functions included
```

## 📦 **Shared FCM Package (`@encreasl/fcm`)**

### **Package Structure**

```typescript
// packages/fcm/src/index.ts
export * from './types';      // Shared types
export * from './client';     // Client utilities & hooks
export * from './server';     // Server utilities
export * from './config';     // Configuration helpers
export * from './utils';      // Common utilities
```

### **Usage Examples**

```typescript
// In apps/web-admin/src/components/NotificationCenter.tsx
import { useFCM, FCMConfig } from '@encreasl/fcm';

function NotificationCenter() {
  const { initialize, requestPermission, subscribeToTopic } = useFCM(config);
  // ... component logic
}

// In functions/src/triggers/custom-notifications.ts
import { createFCMMessage, validateFCMToken } from '@encreasl/fcm/utils';
import { FCMMessage, NotificationPriority } from '@encreasl/fcm/types';
```

### **Configuration Management**

```typescript
// Shared configuration across monorepo
import { createFCMConfig, validateFCMEnvironment } from '@encreasl/fcm/config';

// Auto-validates and creates type-safe config
const fcmConfig = createFCMConfig(process.env);
```

- ✅ **Real-time notifications** for both web and admin apps
- ✅ **Cloud Functions triggers** for automatic notifications
- ✅ **Topic-based messaging** for user segmentation
- ✅ **Admin-specific notifications** with priority handling
- ✅ **Service workers** for background notifications
- ✅ **Type-safe implementation** with TypeScript
- ✅ **Comprehensive analytics** and logging

## 🏗️ Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Firebase Cloud Messaging                 │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────┐    ┌─────────────────┐                │
│  │   Web App       │    │   Admin App     │                │
│  │   (Port 3000)   │    │   (Port 3001)   │                │
│  │                 │    │                 │                │
│  │ • FCM Client    │    │ • Admin FCM     │                │
│  │ • Service Worker│    │ • Priority      │                │
│  │ • Notifications │    │ • Enhanced UI   │                │
│  └─────────────────┘    └─────────────────┘                │
│           │                       │                        │
│           └───────────┬───────────┘                        │
│                       │                                    │
│  ┌─────────────────────────────────────────────────────────┤
│  │              Cloud Functions                            │
│  │                                                         │
│  │ • Contact Form Triggers                                 │
│  │ • Newsletter Triggers                                   │
│  │ • User Management Triggers                              │
│  │ • Scheduled Functions                                   │
│  │ • HTTP Endpoints                                        │
│  └─────────────────────────────────────────────────────────┤
│                       │                                    │
│  ┌─────────────────────────────────────────────────────────┤
│  │                 Firestore                               │
│  │                                                         │
│  │ • Notification Logs                                     │
│  │ • User Preferences                                      │
│  │ • FCM Tokens                                            │
│  │ • Templates                                             │
│  └─────────────────────────────────────────────────────────┘
│                                                             │
└─────────────────────────────────────────────────────────────┘
```

## ⚙️ Setup & Configuration

### 1. **Firebase Project Setup**

1. **Create Firebase Project**:
   ```bash
   # Go to https://console.firebase.google.com
   # Create new project: "encreasl-production"
   ```

2. **Enable Cloud Messaging**:
   - Go to Project Settings > Cloud Messaging
   - Generate VAPID key pair
   - Add to environment variables

3. **Generate Service Account**:
   ```bash
   # Go to Project Settings > Service Accounts
   # Generate new private key
   # Download JSON file
   ```

### 2. **Environment Variables**

Add to your `.env.local` files:

```bash
# Firebase Cloud Messaging
NEXT_PUBLIC_FIREBASE_VAPID_KEY=your_vapid_key_here

# Firebase Admin (Server-side)
FIREBASE_ADMIN_PROJECT_ID=your_project_id
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n"
FIREBASE_ADMIN_CLIENT_EMAIL=<EMAIL>
```

### 3. **Install Dependencies**

```bash
# Root dependencies
pnpm install

# Functions dependencies
cd functions
npm install
```

## ☁️ Cloud Functions

### **Firestore Triggers**

Our Cloud Functions automatically send notifications when:

1. **Contact Form Submitted** (`onContactSubmitted`):
   - Notifies admin team
   - Sends confirmation to user (if opted in)
   - Handles urgent contacts with special alerts

2. **Newsletter Subscription** (`onNewsletterSubscribed`):
   - Welcomes new subscriber
   - Notifies marketing team
   - Tracks milestones

3. **User Created/Deleted** (`onUserCreated`, `onUserDeleted`):
   - Sends welcome notifications
   - Manages onboarding sequence
   - Notifies admin team

### **Scheduled Functions**

- **Daily Digest** (`sendDailyDigest`): Summary of daily activities
- **Weekly Report** (`sendWeeklyReport`): Analytics and insights
- **Cleanup** (`cleanupOldNotifications`): Remove old notification logs

### **HTTP Endpoints**

- **Send Custom Notification**: `/sendNotification`
- **Manage FCM Tokens**: `/manageFCMTokens`
- **Analytics**: `/getNotificationAnalytics`

### **Deployment**

```bash
# Deploy all functions
firebase deploy --only functions

# Deploy specific function
firebase deploy --only functions:onContactSubmitted

# Test locally
cd functions
npm run serve
```

## 📱 Client Integration

### **Web App Integration**

```typescript
// Initialize FCM for user
import { useFCM } from '@/hooks/useFCM';

function MyComponent() {
  const { 
    isInitialized, 
    initialize, 
    subscribeToTopic 
  } = useFCM();

  useEffect(() => {
    if (!isInitialized) {
      initialize();
    }
  }, []);

  return <NotificationSetup />;
}
```

### **Admin App Integration**

```typescript
// Initialize admin FCM
import { useAdminFCM } from '@/hooks/useFCM';

function AdminDashboard() {
  const { 
    urgentMessageCount,
    unreadMessageCount,
    initialize 
  } = useAdminFCM();

  return <AdminNotificationCenter />;
}
```

## 🔧 Service Workers

### **Web App Service Worker** (`/firebase-messaging-sw.js`)

Features:
- Background message handling
- Notification click actions
- Analytics tracking
- Offline support

### **Admin App Service Worker**

Enhanced features:
- Priority-based notifications
- Admin-specific actions (Respond, Assign, Investigate)
- Urgent alert handling
- Session management

### **Registration**

Service workers are automatically registered when FCM is initialized:

```typescript
// Automatic registration
const registration = await navigator.serviceWorker.register('/firebase-messaging-sw.js');
```

## 🛠️ API Routes

### **Token Management**

```typescript
// Save FCM token
POST /api/fcm/token
{
  "userId": "user123",
  "token": "fcm_token_here",
  "platform": "web"
}

// Remove FCM token
DELETE /api/fcm/token
{
  "userId": "user123",
  "token": "fcm_token_here"
}
```

### **Topic Management**

```typescript
// Subscribe to topic
POST /api/fcm/topics/subscribe
{
  "topic": "marketing",
  "userId": "user123"
}

// Unsubscribe from topic
POST /api/fcm/topics/unsubscribe
{
  "topic": "marketing",
  "userId": "user123"
}
```

### **Admin Endpoints**

```typescript
// Admin token management
POST /api/admin/fcm/token
Authorization: Bearer <admin_token>
{
  "userId": "admin123",
  "token": "admin_fcm_token",
  "permissions": ["contact_form", "system_alerts"]
}
```

## 🎨 Components

### **NotificationSetup** (Web App)

- Permission request dialog
- Preference management
- Topic subscription
- Error handling

### **AdminNotificationCenter** (Admin App)

- Real-time notification display
- Urgent message alerts
- Notification history
- Quick actions

### **Usage Examples**

```typescript
// Web app
import { NotificationSetup } from '@/components/NotificationSetup';

function UserDashboard() {
  return (
    <div>
      <h1>Dashboard</h1>
      <NotificationSetup />
    </div>
  );
}

// Admin app
import { AdminNotificationCenter } from '@/components/AdminNotificationCenter';

function AdminHeader() {
  return (
    <header>
      <nav>
        <AdminNotificationCenter />
      </nav>
    </header>
  );
}
```

## 🧪 Testing

### **Local Testing**

```bash
# Start Firebase emulators
firebase emulators:start

# Test functions locally
cd functions
npm run test

# Test FCM in browser
# Open browser dev tools > Application > Service Workers
```

### **Function Testing**

```typescript
// Test contact notification
const testData = {
  name: "Test User",
  email: "<EMAIL>",
  message: "Test message"
};

// Trigger function
await admin.firestore().collection('contacts').add(testData);
```

### **Client Testing**

```typescript
// Test FCM token generation
const token = await getFCMToken();
console.log('FCM Token:', token);

// Test notification display
showForegroundNotification({
  notification: {
    title: "Test Notification",
    body: "This is a test message"
  }
});
```

## 🚀 Deployment

### **Production Deployment**

1. **Deploy Functions**:
   ```bash
   firebase deploy --only functions --project production
   ```

2. **Deploy Apps**:
   ```bash
   # Build and deploy web apps
   pnpm build
   firebase deploy --only hosting --project production
   ```

3. **Configure Environment**:
   ```bash
   # Set production environment variables
   firebase functions:config:set \
     firebase.project_id="your-prod-project" \
     firebase.private_key="your-private-key" \
     --project production
   ```

### **Staging Deployment**

```bash
# Deploy to staging
firebase use staging
firebase deploy --only functions,hosting
```

## 🔍 Troubleshooting

### **Common Issues**

1. **Service Worker Not Registering**:
   ```javascript
   // Check if service worker is supported
   if ('serviceWorker' in navigator) {
     // Register service worker
   }
   ```

2. **Notifications Not Appearing**:
   ```javascript
   // Check permission status
   console.log('Permission:', Notification.permission);
   
   // Check if FCM is initialized
   console.log('FCM initialized:', !!messaging);
   ```

3. **Token Not Saving**:
   ```javascript
   // Verify API endpoint
   const response = await fetch('/api/fcm/token', {
     method: 'POST',
     headers: { 'Content-Type': 'application/json' },
     body: JSON.stringify({ userId, token, platform })
   });
   ```

4. **Functions Not Triggering**:
   ```bash
   # Check function logs
   firebase functions:log --project your-project
   
   # Check Firestore rules
   # Ensure functions have proper permissions
   ```

### **Debug Tools**

1. **Browser DevTools**:
   - Application > Service Workers
   - Application > Notifications
   - Console for error messages

2. **Firebase Console**:
   - Functions logs
   - Cloud Messaging test messages
   - Firestore data

3. **Local Emulators**:
   ```bash
   firebase emulators:start --inspect-functions
   ```

## 📊 Analytics & Monitoring

### **Notification Analytics**

Track notification performance:
- Delivery rates
- Click-through rates
- User engagement
- Error rates

### **Monitoring**

Set up alerts for:
- Function failures
- High error rates
- Token registration issues
- Service worker problems

## 🔐 Security Considerations

1. **VAPID Keys**: Keep private keys secure
2. **Token Management**: Regularly clean up old tokens
3. **Admin Permissions**: Verify admin status before sending sensitive notifications
4. **Rate Limiting**: Implement rate limiting for notification endpoints
5. **Data Privacy**: Follow GDPR/privacy regulations for notification data

## 📚 Additional Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Service Worker API](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)
- [Web Push Protocol](https://tools.ietf.org/html/rfc8030)
- [Firebase Functions Documentation](https://firebase.google.com/docs/functions)
