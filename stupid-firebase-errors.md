ZodError
    at get error (webpack-internal:///(app-pages-browser)/../../node_modules/zod/v3/types.js:134:31)
    at ZodObject.parse (webpack-internal:///(app-pages-browser)/../../node_modules/zod/v3/types.js:209:22)
    at validateAdminClientEnv (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:194:37)
    at FirebaseProvider.useEffect (webpack-internal:///(app-pages-browser)/./src/components/FirebaseProvider.tsx:27:98)
    at Object.react_stack_bottom_frame (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:23638:20)
    at runWithFiberInDEV (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:873:30)
    at commitHookEffectListMount (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12296:29)
    at commitHookPassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12417:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14338:13)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)



    Error: Validation issues:
    at createConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/shared/console-error.js:23:71)
    at handleConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/use-error-handler.js:45:54)
    at console.error (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/intercept-console-error.js:50:57)
    at validateAdminClientEnv (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:199:21)
    at FirebaseProvider.useEffect (webpack-internal:///(app-pages-browser)/./src/components/FirebaseProvider.tsx:27:98)
    at Object.react_stack_bottom_frame (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:23638:20)
    at runWithFiberInDEV (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:873:30)
    at commitHookEffectListMount (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12296:29)
    at commitHookPassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12417:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14338:13)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)


    Error:   1. NEXT_PUBLIC_FIREBASE_API_KEY: Required
    at createConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/shared/console-error.js:23:71)
    at handleConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/use-error-handler.js:45:54)
    at console.error (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/intercept-console-error.js:50:57)
    at eval (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:201:25)
    at Array.forEach (<anonymous>)
    at validateAdminClientEnv (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:200:29)
    at FirebaseProvider.useEffect (webpack-internal:///(app-pages-browser)/./src/components/FirebaseProvider.tsx:27:98)
    at Object.react_stack_bottom_frame (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:23638:20)
    at runWithFiberInDEV (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:873:30)
    at commitHookEffectListMount (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12296:29)
    at commitHookPassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12417:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14338:13)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)


    Error:   2. NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: Required
    at createConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/shared/console-error.js:23:71)
    at handleConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/use-error-handler.js:45:54)
    at console.error (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/intercept-console-error.js:50:57)
    at eval (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:201:25)
    at Array.forEach (<anonymous>)
    at validateAdminClientEnv (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:200:29)
    at FirebaseProvider.useEffect (webpack-internal:///(app-pages-browser)/./src/components/FirebaseProvider.tsx:27:98)
    at Object.react_stack_bottom_frame (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:23638:20)
    at runWithFiberInDEV (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:873:30)
    at commitHookEffectListMount (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12296:29)
    at commitHookPassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12417:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14338:13)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)




    Error:   3. NEXT_PUBLIC_FIREBASE_PROJECT_ID: Required
    at createConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/shared/console-error.js:23:71)
    at handleConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/use-error-handler.js:45:54)
    at console.error (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/intercept-console-error.js:50:57)
    at eval (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:201:25)
    at Array.forEach (<anonymous>)
    at validateAdminClientEnv (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:200:29)
    at FirebaseProvider.useEffect (webpack-internal:///(app-pages-browser)/./src/components/FirebaseProvider.tsx:27:98)
    at Object.react_stack_bottom_frame (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:23638:20)
    at runWithFiberInDEV (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:873:30)
    at commitHookEffectListMount (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12296:29)
    at commitHookPassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12417:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14338:13)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)



    Error:   4. NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: Required
    at createConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/shared/console-error.js:23:71)
    at handleConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/use-error-handler.js:45:54)
    at console.error (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/intercept-console-error.js:50:57)
    at eval (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:201:25)
    at Array.forEach (<anonymous>)
    at validateAdminClientEnv (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:200:29)
    at FirebaseProvider.useEffect (webpack-internal:///(app-pages-browser)/./src/components/FirebaseProvider.tsx:27:98)
    at Object.react_stack_bottom_frame (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:23638:20)
    at runWithFiberInDEV (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:873:30)
    at commitHookEffectListMount (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12296:29)
    at commitHookPassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12417:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14338:13)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)




    Error:   5. NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: Required
    at createConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/shared/console-error.js:23:71)
    at handleConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/use-error-handler.js:45:54)
    at console.error (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/intercept-console-error.js:50:57)
    at eval (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:201:25)
    at Array.forEach (<anonymous>)
    at validateAdminClientEnv (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:200:29)
    at FirebaseProvider.useEffect (webpack-internal:///(app-pages-browser)/./src/components/FirebaseProvider.tsx:27:98)
    at Object.react_stack_bottom_frame (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:23638:20)
    at runWithFiberInDEV (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:873:30)
    at commitHookEffectListMount (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12296:29)
    at commitHookPassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12417:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14338:13)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)



    Error:   6. NEXT_PUBLIC_FIREBASE_APP_ID: Required
    at createConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/shared/console-error.js:23:71)
    at handleConsoleError (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/use-error-handler.js:45:54)
    at console.error (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/next-devtools/userspace/app/errors/intercept-console-error.js:50:57)
    at eval (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:201:25)
    at Array.forEach (<anonymous>)
    at validateAdminClientEnv (webpack-internal:///(app-pages-browser)/../../packages/env/src/index.ts:200:29)
    at FirebaseProvider.useEffect (webpack-internal:///(app-pages-browser)/./src/components/FirebaseProvider.tsx:27:98)
    at Object.react_stack_bottom_frame (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:23638:20)
    at runWithFiberInDEV (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:873:30)
    at commitHookEffectListMount (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12296:29)
    at commitHookPassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:12417:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14338:13)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14465:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14341:11)
    at recursivelyTraversePassiveMountEffects (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14311:11)
    at commitPassiveMountOnFiber (webpack-internal:///(app-pages-browser)/../../node_modules/next/dist/compiled/react-dom/cjs/react-dom-client.development.js:14331:11)