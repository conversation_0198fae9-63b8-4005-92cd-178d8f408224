{"version": 3, "file": "user-notifications.js", "sourceRoot": "", "sources": ["../../src/triggers/user-notifications.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,+DAAuF;AACvF,8CAA+C;AAC/C,yDAAqD;AACrD,wDAAwD;AAGxD,MAAM,UAAU,GAAG,IAAI,wBAAU,EAAE,CAAC;AACpC,MAAM,EAAE,GAAG,IAAA,wBAAY,GAAE,CAAC;AAG1B,2CAA2C;AAC3C,uBAAuB;AACvB,2CAA2C;AAE9B,QAAA,aAAa,GAAG,IAAA,6BAAiB,EAC5C;IACE,QAAQ,EAAE,gBAAgB;IAC1B,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,EAAE;CACnB,EACD,KAAK,EAAE,KAAK,EAAE,EAAE;;IACd,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAA,KAAK,CAAC,IAAI,0CAAE,IAAI,EAAE,CAAC;QACpC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;QAEnC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,WAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACnC,OAAO;QACT,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,iCAAiC,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QAEjE,2CAA2C;QAC3C,oCAAoC;QACpC,2CAA2C;QAE3C,uDAAuD;QACvD,MAAM,kBAAkB,GAAG;YACzB,MAAM;YACN,KAAK,EAAE,QAAQ,CAAC,KAAK;YACrB,SAAS,EAAE,EAAE;YACb,MAAM,EAAE,CAAC,SAAS,CAAC;YACnB,WAAW,EAAE;gBACX,WAAW,EAAE,KAAK,EAAE,8CAA8C;gBAClE,UAAU,EAAE,CAAA,MAAA,MAAA,QAAQ,CAAC,WAAW,0CAAE,aAAa,0CAAE,SAAS,KAAI,IAAI;gBAClE,WAAW,EAAE,CAAA,MAAA,MAAA,QAAQ,CAAC,WAAW,0CAAE,aAAa,0CAAE,KAAK,KAAI,IAAI;gBAC/D,SAAS,EAAE,KAAK;gBAChB,WAAW,EAAE,KAAK;gBAClB,YAAY,EAAE,KAAK;gBACnB,YAAY,EAAE,CAAA,MAAA,MAAA,QAAQ,CAAC,WAAW,0CAAE,aAAa,0CAAE,IAAI,KAAI,KAAK;gBAChE,SAAS,EAAE,CAAA,MAAA,MAAA,QAAQ,CAAC,WAAW,0CAAE,aAAa,0CAAE,SAAS,KAAI,IAAI;aAClE;YACD,QAAQ,EAAE,CAAA,MAAA,QAAQ,CAAC,WAAW,0CAAE,QAAQ,KAAI,KAAK;YACjD,QAAQ,EAAE,CAAA,MAAA,QAAQ,CAAC,WAAW,0CAAE,QAAQ,KAAI,IAAI;YAChD,UAAU,EAAE;gBACV,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE,OAAO;gBACd,GAAG,EAAE,OAAO;aACb;YACD,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,MAAM,EAAE,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;QAEpF,oDAAoD;QACpD,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,MAAM,mBAAmB,GAAG;gBAC1B,YAAY,EAAE;oBACZ,KAAK,EAAE,yBAAyB;oBAChC,IAAI,EAAE,wFAAwF;oBAC9F,IAAI,EAAE,yBAAyB;oBAC/B,KAAK,EAAE,4BAA4B;oBACnC,WAAW,EAAE,qBAAqB;iBACnC;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,cAAc;oBACpB,MAAM;oBACN,KAAK,EAAE,QAAQ,CAAC,KAAK;oBACrB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;iBACjC;aACF,CAAC;YAEF,MAAM,aAAa,GAAG,MAAM,UAAU,CAAC,YAAY,CACjD,QAAQ,CAAC,SAAS,EAClB,mBAAmB,EACnB,QAAQ,CACT,CAAC;YAEF,WAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,aAAa,CAAC,CAAC;YAE3D,6BAA6B;YAC7B,MAAM,UAAU,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YACjE,MAAM,UAAU,CAAC,gBAAgB,CAAC,QAAQ,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;QACrE,CAAC;QAED,2CAA2C;QAC3C,oBAAoB;QACpB,2CAA2C;QAE3C,wDAAwD;QACxD,MAAM,eAAe,GAAG,MAAM,EAAE;aAC7B,UAAU,CAAC,0BAA0B,CAAC;aACtC,KAAK,CAAC,yBAAyB,EAAE,IAAI,EAAE,IAAI,CAAC;aAC5C,KAAK,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,CAAC;aAC1C,GAAG,EAAE,CAAC;QAET,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACjC,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;YACzB,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClD,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;YACvC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,iBAAiB,GAAG;gBACxB,YAAY,EAAE;oBACZ,KAAK,EAAE,iBAAiB;oBACxB,IAAI,EAAE,GAAG,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,KAAK,uBAAuB;oBACtE,IAAI,EAAE,6BAA6B;oBACnC,WAAW,EAAE,gBAAgB,MAAM,EAAE;iBACtC;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,aAAa;oBACnB,MAAM;oBACN,SAAS,EAAE,QAAQ,CAAC,KAAK;oBACzB,QAAQ,EAAE,QAAQ,CAAC,WAAW,IAAI,EAAE;oBACpC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;iBACjC;aACF,CAAC;YAEF,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,YAAY,CAC/C,WAAW,EACX,iBAAiB,EACjB,KAAK,CACN,CAAC;YAEF,WAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,WAAW,CAAC,CAAC;QACzD,CAAC;QAED,2CAA2C;QAC3C,sBAAsB;QACtB,2CAA2C;QAE3C,yEAAyE;QACzE,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACxD,8BAA8B;YAC9B,UAAU,CAAC,KAAK,IAAI,EAAE;gBACpB,MAAM,sBAAsB,GAAG;oBAC7B,YAAY,EAAE;wBACZ,KAAK,EAAE,+BAA+B;wBACtC,IAAI,EAAE,wEAAwE;wBAC9E,IAAI,EAAE,sBAAsB;wBAC5B,WAAW,EAAE,kBAAkB;qBAChC;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,iBAAiB;wBACvB,MAAM;wBACN,GAAG,EAAE,GAAG;wBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;qBACjC;iBACF,CAAC;gBAEF,MAAM,UAAU,CAAC,YAAY,CAC3B,QAAQ,CAAC,SAAS,EAClB,sBAAsB,EACtB,QAAQ,CACT,CAAC;YACJ,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,WAAW;YAEpC,4BAA4B;YAC5B,UAAU,CAAC,KAAK,IAAI,EAAE;gBACpB,MAAM,mBAAmB,GAAG;oBAC1B,YAAY,EAAE;wBACZ,KAAK,EAAE,4BAA4B;wBACnC,IAAI,EAAE,mEAAmE;wBACzE,IAAI,EAAE,0BAA0B;wBAChC,WAAW,EAAE,sBAAsB;qBACpC;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,mBAAmB;wBACzB,MAAM;wBACN,GAAG,EAAE,GAAG;wBACR,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;qBACjC;iBACF,CAAC;gBAEF,MAAM,UAAU,CAAC,YAAY,CAC3B,QAAQ,CAAC,SAAS,EAClB,mBAAmB,EACnB,QAAQ,CACT,CAAC;YACJ,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS;QACxC,CAAC;QAED,2CAA2C;QAC3C,0BAA0B;QAC1B,2CAA2C;QAE3C,wCAAwC;QACxC,MAAM,UAAU,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,CAAC;QACtD,MAAM,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC;QAClC,MAAM,UAAU,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;QAEvE,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACnC,MAAM,qBAAqB,GAAG;gBAC5B,YAAY,EAAE;oBACZ,KAAK,EAAE,4BAA4B;oBACnC,IAAI,EAAE,gCAAgC,SAAS,CAAC,cAAc,EAAE,oBAAoB;oBACpF,IAAI,EAAE,2BAA2B;oBACjC,KAAK,EAAE,4BAA4B;oBACnC,WAAW,EAAE,wBAAwB;iBACtC;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,gBAAgB;oBACtB,SAAS,EAAE,SAAS,CAAC,QAAQ,EAAE;oBAC/B,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;oBAChC,kBAAkB,EAAE,MAAM;iBAC3B;aACF,CAAC;YAEF,MAAM,UAAU,CAAC,WAAW,CAAC,OAAO,EAAE,qBAAqB,EAAE,MAAM,CAAC,CAAC;YAErE,gBAAgB;YAChB,MAAM,EAAE,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC;gBACpC,IAAI,EAAE,aAAa;gBACnB,KAAK,EAAE,SAAS;gBAChB,UAAU,EAAE,IAAI,IAAI,EAAE;gBACtB,gBAAgB,EAAE,IAAI;aACvB,CAAC,CAAC;QACL,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,2CAA2C,MAAM,EAAE,CAAC,CAAC;IAEnE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,WAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;QACpE,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CACF,CAAC;AAEF,2CAA2C;AAC3C,uBAAuB;AACvB,2CAA2C;AAE9B,QAAA,aAAa,GAAG,IAAA,6BAAiB,EAC5C;IACE,QAAQ,EAAE,gBAAgB;IAC1B,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,EAAE;CACnB,EACD,KAAK,EAAE,KAAK,EAAE,EAAE;;IACd,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAA,KAAK,CAAC,IAAI,0CAAE,IAAI,EAAE,CAAC;QACpC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;QAEnC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,WAAM,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;YAChD,OAAO;QACT,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,6BAA6B,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QAE7D,2CAA2C;QAC3C,oBAAoB;QACpB,2CAA2C;QAE3C,kCAAkC;QAClC,MAAM,EAAE,CAAC,UAAU,CAAC,0BAA0B,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,EAAE,CAAC;QAErE,qBAAqB;QACrB,MAAM,YAAY,GAAG,MAAM,EAAE;aAC1B,UAAU,CAAC,aAAa,CAAC;aACzB,KAAK,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC;aAC7B,GAAG,EAAE,CAAC;QAET,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,EAAE,CAAC;QACzB,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YAC9B,KAAK,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC,CAAC,CAAC;QACH,MAAM,KAAK,CAAC,MAAM,EAAE,CAAC;QAErB,2CAA2C;QAC3C,oBAAoB;QACpB,2CAA2C;QAE3C,MAAM,eAAe,GAAG,MAAM,EAAE;aAC7B,UAAU,CAAC,0BAA0B,CAAC;aACtC,KAAK,CAAC,yBAAyB,EAAE,IAAI,EAAE,IAAI,CAAC;aAC5C,KAAK,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,CAAC;aAC1C,GAAG,EAAE,CAAC;QAET,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACjC,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;YACzB,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClD,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;YACvC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,iBAAiB,GAAG;gBACxB,YAAY,EAAE;oBACZ,KAAK,EAAE,sBAAsB;oBAC7B,IAAI,EAAE,GAAG,QAAQ,CAAC,WAAW,IAAI,QAAQ,CAAC,KAAK,4BAA4B;oBAC3E,IAAI,EAAE,8BAA8B;oBACpC,WAAW,EAAE,sBAAsB;iBACpC;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,cAAc;oBACpB,MAAM;oBACN,SAAS,EAAE,QAAQ,CAAC,KAAK;oBACzB,QAAQ,EAAE,QAAQ,CAAC,WAAW,IAAI,EAAE;oBACpC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;iBACjC;aACF,CAAC;YAEF,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,YAAY,CAC/C,WAAW,EACX,iBAAiB,EACjB,KAAK,CACN,CAAC;YAEF,WAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE,WAAW,CAAC,CAAC;QAClE,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,2CAA2C,MAAM,EAAE,CAAC,CAAC;IAEnE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,WAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE,KAAK,CAAC,CAAC;QACpE,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CACF,CAAC"}