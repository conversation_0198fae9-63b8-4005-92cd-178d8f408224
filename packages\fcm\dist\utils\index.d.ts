/**
 * Shared FCM Utilities
 *
 * Utility functions that can be used in both client and server environments
 * for Firebase Cloud Messaging operations.
 */
import type { FCMConfig, FCMMessage, NotificationPayload, NotificationData, FCMValidationResult } from '../types';
/**
 * Validate FCM configuration
 */
export declare function validateFCMConfig(config: Partial<FCMConfig>): FCMValidationResult;
/**
 * Validate notification payload
 */
export declare function validateNotificationPayload(payload: Partial<NotificationPayload>): FCMValidationResult;
/**
 * Validate FCM token format
 */
export declare function validateFCMToken(token: string): boolean;
/**
 * Validate topic name
 */
export declare function validateTopicName(topic: string): boolean;
/**
 * Create a standardized FCM message
 */
export declare function createFCMMessage(notification: NotificationPayload, data?: NotificationData, options?: {
    android?: any;
    webpush?: any;
    apns?: any;
}): FCMMessage;
/**
 * Create notification data with proper string conversion
 */
export declare function formatNotificationData(data: Record<string, any>): NotificationData;
/**
 * Create a web push notification with proper formatting
 */
export declare function createWebPushNotification(notification: NotificationPayload, data?: NotificationData, options?: {
    requireInteraction?: boolean;
    silent?: boolean;
    actions?: Array<{
        action: string;
        title: string;
        icon?: string;
    }>;
    link?: string;
}): FCMMessage;
/**
 * Create an Android notification with proper formatting
 */
export declare function createAndroidNotification(notification: NotificationPayload, data?: NotificationData, options?: {
    priority?: "normal" | "high";
    channelId?: string;
    sound?: string;
    color?: string;
}): FCMMessage;
/**
 * Generate a unique notification ID
 */
export declare function generateNotificationId(): string;
/**
 * Check if code is running in browser environment
 */
export declare function isBrowser(): boolean;
/**
 * Check if notifications are supported in current environment
 */
export declare function isNotificationSupported(): boolean;
/**
 * Check if service workers are supported
 */
export declare function isServiceWorkerSupported(): boolean;
/**
 * Get notification permission status
 */
export declare function getNotificationPermission(): NotificationPermission | null;
/**
 * Format timestamp for notifications
 */
export declare function formatNotificationTimestamp(date?: Date): string;
/**
 * Sanitize notification content
 */
export declare function sanitizeNotificationContent(content: string): string;
/**
 * Create notification analytics data
 */
export declare function createNotificationAnalytics(notificationId: string, userId?: string, metadata?: Record<string, any>): NotificationData;
/**
 * Parse FCM error and provide user-friendly message
 */
export declare function parseFCMError(error: any): string;
/**
 * Check if error is retryable
 */
export declare function isRetryableError(error: any): boolean;
//# sourceMappingURL=index.d.ts.map