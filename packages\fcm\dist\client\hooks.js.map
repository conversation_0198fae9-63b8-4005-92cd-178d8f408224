{"version": 3, "file": "hooks.js", "sourceRoot": "", "sources": ["../../src/client/hooks.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoBH,wBA8NC;AASD,8DAsEC;AASD,gEAgEC;AAxYD,+BAAiE;AACjE,gDAAuF;AACvF,oCAAsD;AAOtD,oCAAyC;AAEzC,2CAA2C;AAC3C,gBAAgB;AAChB,2CAA2C;AAE3C;;GAEG;AACH,SAAgB,MAAM,CAAC,MAAiB;IAAxC,iBA8NC;IA7NO,IAAA,KAAoB,IAAA,gBAAQ,EAAe;QAC/C,aAAa,EAAE,KAAK;QACpB,WAAW,EAAE,KAAK;QAClB,UAAU,EAAE,IAAI;QAChB,KAAK,EAAE,IAAI;QACX,KAAK,EAAE,IAAI;QACX,SAAS,EAAE,IAAI;KAChB,CAAC,EAPK,KAAK,QAAA,EAAE,QAAQ,QAOpB,CAAC;IAEH,IAAM,YAAY,GAAG,IAAA,cAAM,EAAM,IAAI,CAAC,CAAC;IACvC,IAAM,cAAc,GAAG,IAAA,cAAM,EAAsB,IAAI,CAAC,CAAC;IAEzD,8BAA8B;IAC9B,IAAM,UAAU,GAAG,IAAA,mBAAW,EAAC;;;;;;oBAE3B,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBAAM,IAAI,KAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,IAAG,EAA3C,CAA2C,CAAC,CAAC;oBAE9D,8BAA8B;oBAC9B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;wBAClC,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBACZ,IAAI,KACP,WAAW,EAAE,KAAK,EAClB,SAAS,EAAE,KAAK,EAChB,KAAK,EAAE,iDAAiD,IACxD,EALe,CAKf,CAAC,CAAC;wBACJ,sBAAO;oBACT,CAAC;oBAED,uCAAuC;oBACvC,IAAI,CAAC,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI,CAAC,CAAC,eAAe,IAAI,SAAS,CAAC,EAAE,CAAC;wBACnE,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBACZ,IAAI,KACP,WAAW,EAAE,KAAK,EAClB,SAAS,EAAE,KAAK,EAChB,KAAK,EAAE,gDAAgD,IACvD,EALe,CAKf,CAAC,CAAC;wBACJ,sBAAO;oBACT,CAAC;oBAGG,GAAG,SAAA,CAAC;oBACF,YAAY,GAAG,IAAA,aAAO,GAAE,CAAC;oBAC/B,IAAI,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBAC5B,GAAG,GAAG,YAAY,CAAC,CAAC,CAAC,CAAC;oBACxB,CAAC;yBAAM,CAAC;wBACN,GAAG,GAAG,IAAA,mBAAa,EAAC,MAAM,CAAC,CAAC;oBAC9B,CAAC;oBAGK,SAAS,GAAG,IAAA,wBAAY,EAAC,GAAG,CAAC,CAAC;oBACpC,YAAY,CAAC,OAAO,GAAG,SAAS,CAAC;yBAG7B,CAAA,eAAe,IAAI,SAAS,CAAA,EAA5B,wBAA4B;;;;oBAE5B,qBAAM,SAAS,CAAC,aAAa,CAAC,QAAQ,CAAC,qBAAY,CAAC,mBAAmB,EAAE;4BACvE,KAAK,EAAE,qBAAY,CAAC,oBAAoB;yBACzC,CAAC,EAAA;;oBAFF,SAEE,CAAC;;;;oBAEH,OAAO,CAAC,IAAI,CAAC,qCAAqC,EAAE,SAAO,CAAC,CAAC;;;oBAK3D,eAAa,YAAY,CAAC,UAAU,CAAC;oBAE3C,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBACZ,IAAI,KACP,aAAa,EAAE,IAAI,EACnB,WAAW,EAAE,IAAI,EACjB,UAAU,cAAA,EACV,SAAS,EAAE,KAAK,IAChB,EANe,CAMf,CAAC,CAAC;oBAGE,WAAW,GAAG,IAAA,qBAAS,EAAC,SAAS,EAAE,UAAC,OAAuB;wBAC/D,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,OAAO,CAAC,CAAC;wBAErD,kCAAkC;wBAClC,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;4BACzB,gBAAgB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;wBACzC,CAAC;oBACH,CAAC,CAAC,CAAC;oBAEH,cAAc,CAAC,OAAO,GAAG,WAAW,CAAC;;;;oBAGrC,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,OAAK,CAAC,CAAC;oBAClD,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBACZ,IAAI,KACP,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,0BAA0B,EAC1E,SAAS,EAAE,KAAK,IAChB,EAJe,CAIf,CAAC,CAAC;;;;;SAEP,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;IAEb,kCAAkC;IAClC,IAAM,iBAAiB,GAAG,IAAA,mBAAW,EAAC;;;;;;oBAElC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;wBACvB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;oBACjD,CAAC;oBAEkB,qBAAM,YAAY,CAAC,iBAAiB,EAAE,EAAA;;oBAAnD,eAAa,SAAsC;oBACzD,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBAAM,IAAI,KAAE,UAAU,cAAA,IAAG,EAAzB,CAAyB,CAAC,CAAC;oBAE5C,sBAAO,YAAU,KAAK,SAAS,EAAC;;;oBAEhC,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,OAAK,CAAC,CAAC;oBAClD,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBACZ,IAAI,KACP,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,IAC9E,EAHe,CAGf,CAAC,CAAC;oBACJ,sBAAO,KAAK,EAAC;;;;SAEhB,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;IAExB,gBAAgB;IAChB,IAAM,aAAa,GAAG,IAAA,mBAAW,EAAC,UAAO,QAAiB;;;;;;oBAEtD,IAAI,CAAC,YAAY,CAAC,OAAO,IAAI,KAAK,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;wBAC5D,sBAAO,IAAI,EAAC;oBACd,CAAC;oBAEa,qBAAM,IAAA,oBAAQ,EAAC,YAAY,CAAC,OAAO,EAAE;4BACjD,QAAQ,EAAE,QAAQ,IAAI,MAAM,CAAC,QAAQ;yBACtC,CAAC,EAAA;;oBAFI,UAAQ,SAEZ;oBAEF,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBAAM,IAAI,KAAE,KAAK,SAAA,IAAG,EAApB,CAAoB,CAAC,CAAC;oBACvC,sBAAO,OAAK,EAAC;;;oBAEb,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,OAAK,CAAC,CAAC;oBAC/C,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBACZ,IAAI,KACP,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,qBAAqB,IACrE,EAHe,CAGf,CAAC,CAAC;oBACJ,sBAAO,IAAI,EAAC;;;;SAEf,EAAE,CAAC,KAAK,CAAC,UAAU,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;IAExC,qBAAqB;IACrB,IAAM,gBAAgB,GAAG,IAAA,mBAAW,EAAC,UAAO,KAAa;;;;;;oBAEvC,KAAA,KAAK,CAAC,KAAK,CAAA;4BAAX,wBAAW;oBAAI,qBAAM,aAAa,EAAE,EAAA;;0BAArB,SAAqB;;;oBAA5C,KAAK,KAAuC;oBAClD,IAAI,CAAC,KAAK,EAAE,CAAC;wBACX,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;oBAC5C,CAAC;oBAGgB,qBAAM,KAAK,CAAC,oBAAoB,EAAE;4BACjD,MAAM,EAAE,MAAM;4BACd,OAAO,EAAE;gCACP,cAAc,EAAE,kBAAkB;6BACnC;4BACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,OAAA,EAAE,KAAK,OAAA,EAAE,CAAC;yBACvC,CAAC,EAAA;;oBANI,QAAQ,GAAG,SAMf;oBAEF,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;wBACjB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;oBAClD,CAAC;oBAED,sBAAO,IAAI,EAAC;;;oBAEZ,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,OAAK,CAAC,CAAC;oBAClD,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBACZ,IAAI,KACP,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,IAC9E,EAHe,CAGf,CAAC,CAAC;oBACJ,sBAAO,KAAK,EAAC;;;;SAEhB,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC;IAEjC,yBAAyB;IACzB,IAAM,oBAAoB,GAAG,IAAA,mBAAW,EAAC,UAAO,KAAa;;;;;;oBAE3C,KAAA,KAAK,CAAC,KAAK,CAAA;4BAAX,wBAAW;oBAAI,qBAAM,aAAa,EAAE,EAAA;;0BAArB,SAAqB;;;oBAA5C,KAAK,KAAuC;oBAClD,IAAI,CAAC,KAAK,EAAE,CAAC;wBACX,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;oBAC5C,CAAC;oBAGgB,qBAAM,KAAK,CAAC,sBAAsB,EAAE;4BACnD,MAAM,EAAE,MAAM;4BACd,OAAO,EAAE;gCACP,cAAc,EAAE,kBAAkB;6BACnC;4BACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,KAAK,OAAA,EAAE,KAAK,OAAA,EAAE,CAAC;yBACvC,CAAC,EAAA;;oBANI,QAAQ,GAAG,SAMf;oBAEF,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;wBACjB,MAAM,IAAI,KAAK,CAAC,kCAAkC,CAAC,CAAC;oBACtD,CAAC;oBAED,sBAAO,IAAI,EAAC;;;oBAEZ,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,OAAK,CAAC,CAAC;oBACpD,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBACZ,IAAI,KACP,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,kCAAkC,IAClF,EAHe,CAGf,CAAC,CAAC;oBACJ,sBAAO,KAAK,EAAC;;;;SAEhB,EAAE,CAAC,KAAK,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC,CAAC;IAEjC,qBAAqB;IACrB,IAAA,iBAAS,EAAC;QACR,OAAO;YACL,IAAI,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC3B,cAAc,CAAC,OAAO,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,6BACK,KAAK,KACR,UAAU,YAAA,EACV,iBAAiB,mBAAA,EACjB,QAAQ,EAAE,aAAa,EACvB,gBAAgB,kBAAA,EAChB,oBAAoB,sBAAA,IACpB;AACJ,CAAC;AAED,2CAA2C;AAC3C,+BAA+B;AAC/B,2CAA2C;AAE3C;;GAEG;AACH,SAAgB,yBAAyB;IAAzC,iBAsEC;IArEO,IAAA,KAAoB,IAAA,gBAAQ,EAA8B;QAC9D,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,KAAK;QAClB,SAAS,EAAE,IAAI;QACf,KAAK,EAAE,IAAI;KACZ,CAAC,EALK,KAAK,QAAA,EAAE,QAAQ,QAKpB,CAAC;IAEH,IAAA,iBAAS,EAAC;QACR,8BAA8B;QAC9B,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;YAClC,QAAQ,CAAC;gBACP,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,KAAK;gBAClB,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,0CAA0C;aAClD,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,uCAAuC;QACvC,IAAI,CAAC,CAAC,cAAc,IAAI,MAAM,CAAC,EAAE,CAAC;YAChC,QAAQ,CAAC;gBACP,UAAU,EAAE,IAAI;gBAChB,WAAW,EAAE,KAAK;gBAClB,SAAS,EAAE,KAAK;gBAChB,KAAK,EAAE,6BAA6B;aACrC,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,QAAQ,CAAC;YACP,UAAU,EAAE,YAAY,CAAC,UAAU;YACnC,WAAW,EAAE,IAAI;YACjB,SAAS,EAAE,KAAK;YAChB,KAAK,EAAE,IAAI;SACZ,CAAC,CAAC;IACL,CAAC,EAAE,EAAE,CAAC,CAAC;IAEP,IAAM,iBAAiB,GAAG,IAAA,mBAAW,EAAC;;;;;;oBAElC,IAAI,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;wBACvB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;oBACjD,CAAC;oBAED,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBAAM,IAAI,KAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,IAAG,EAA3C,CAA2C,CAAC,CAAC;oBAE3C,qBAAM,YAAY,CAAC,iBAAiB,EAAE,EAAA;;oBAAnD,eAAa,SAAsC;oBAEzD,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBACZ,IAAI,KACP,UAAU,cAAA,EACV,SAAS,EAAE,KAAK,IAChB,EAJe,CAIf,CAAC,CAAC;oBAEJ,sBAAO,YAAU,KAAK,SAAS,EAAC;;;oBAEhC,QAAQ,CAAC,UAAA,IAAI,IAAI,OAAA,uBACZ,IAAI,KACP,KAAK,EAAE,OAAK,YAAY,KAAK,CAAC,CAAC,CAAC,OAAK,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,EAC9E,SAAS,EAAE,KAAK,IAChB,EAJe,CAIf,CAAC,CAAC;oBACJ,sBAAO,KAAK,EAAC;;;;SAEhB,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC;IAExB,6BACK,KAAK,KACR,iBAAiB,mBAAA,IACjB;AACJ,CAAC;AAED,2CAA2C;AAC3C,gCAAgC;AAChC,2CAA2C;AAE3C;;GAEG;AACH,SAAgB,0BAA0B;IAA1C,iBAgEC;IA/DO,IAAA,KAAgC,IAAA,gBAAQ,EAAiC,IAAI,CAAC,EAA7E,WAAW,QAAA,EAAE,cAAc,QAAkD,CAAC;IAC/E,IAAA,KAA4B,IAAA,gBAAQ,EAAC,IAAI,CAAC,EAAzC,SAAS,QAAA,EAAE,YAAY,QAAkB,CAAC;IAC3C,IAAA,KAAoB,IAAA,gBAAQ,EAAgB,IAAI,CAAC,EAAhD,KAAK,QAAA,EAAE,QAAQ,QAAiC,CAAC;IAExD,mBAAmB;IACnB,IAAM,eAAe,GAAG,IAAA,mBAAW,EAAC;;;;;;oBAEhC,YAAY,CAAC,IAAI,CAAC,CAAC;oBACnB,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAEE,qBAAM,KAAK,CAAC,sBAAsB,CAAC,EAAA;;oBAA9C,QAAQ,GAAG,SAAmC;oBACpD,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;wBACjB,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAC;oBAChD,CAAC;oBAEY,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;oBAA5B,IAAI,GAAG,SAAqB;oBAClC,cAAc,CAAC,IAAI,CAAC,CAAC;;;;oBAErB,QAAQ,CAAC,KAAG,YAAY,KAAK,CAAC,CAAC,CAAC,KAAG,CAAC,OAAO,CAAC,CAAC,CAAC,4BAA4B,CAAC,CAAC;;;oBAE5E,YAAY,CAAC,KAAK,CAAC,CAAC;;;;;SAEvB,EAAE,EAAE,CAAC,CAAC;IAEP,qBAAqB;IACrB,IAAM,iBAAiB,GAAG,IAAA,mBAAW,EAAC,UAAO,OAAyC;;;;;;oBAElF,QAAQ,CAAC,IAAI,CAAC,CAAC;oBAEE,qBAAM,KAAK,CAAC,sBAAsB,EAAE;4BACnD,MAAM,EAAE,OAAO;4BACf,OAAO,EAAE;gCACP,cAAc,EAAE,kBAAkB;6BACnC;4BACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;yBAC9B,CAAC,EAAA;;oBANI,QAAQ,GAAG,SAMf;oBAEF,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;wBACjB,MAAM,IAAI,KAAK,CAAC,8BAA8B,CAAC,CAAC;oBAClD,CAAC;oBAE0B,qBAAM,QAAQ,CAAC,IAAI,EAAE,EAAA;;oBAA1C,kBAAkB,GAAG,SAAqB;oBAChD,cAAc,CAAC,kBAAkB,CAAC,CAAC;oBAEnC,sBAAO,IAAI,EAAC;;;oBAEZ,QAAQ,CAAC,KAAG,YAAY,KAAK,CAAC,CAAC,CAAC,KAAG,CAAC,OAAO,CAAC,CAAC,CAAC,8BAA8B,CAAC,CAAC;oBAC9E,sBAAO,KAAK,EAAC;;;;SAEhB,EAAE,EAAE,CAAC,CAAC;IAEP,4BAA4B;IAC5B,IAAA,iBAAS,EAAC;QACR,eAAe,EAAE,CAAC;IACpB,CAAC,EAAE,CAAC,eAAe,CAAC,CAAC,CAAC;IAEtB,OAAO;QACL,WAAW,aAAA;QACX,SAAS,WAAA;QACT,KAAK,OAAA;QACL,eAAe,iBAAA;QACf,iBAAiB,mBAAA;KAClB,CAAC;AACJ,CAAC;AAED,2CAA2C;AAC3C,mBAAmB;AACnB,2CAA2C;AAE3C;;GAEG;AACH,SAAS,gBAAgB,CAAC,YAAiB;IACzC,IAAI,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE,CAAC;QAC1C,IAAI,YAAY,CAAC,YAAY,CAAC,KAAK,EAAE;YACnC,IAAI,EAAE,YAAY,CAAC,IAAI;YACvB,IAAI,EAAE,YAAY,CAAC,IAAI,IAAI,qBAAY,CAAC,yBAAyB;YACjE,KAAK,EAAE,YAAY,CAAC,KAAK,IAAI,qBAAY,CAAC,0BAA0B;YACpE,GAAG,EAAE,YAAY,CAAC,GAAG;YACrB,IAAI,EAAE,YAAY,CAAC,IAAI;SACxB,CAAC,CAAC;IACL,CAAC;AACH,CAAC"}