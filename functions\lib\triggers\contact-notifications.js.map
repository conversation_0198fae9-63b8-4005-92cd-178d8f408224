{"version": 3, "file": "contact-notifications.js", "sourceRoot": "", "sources": ["../../src/triggers/contact-notifications.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,+DAAoE;AACpE,8CAA+C;AAC/C,yDAAqD;AACrD,wDAAwD;AAExD,MAAM,UAAU,GAAG,IAAI,wBAAU,EAAE,CAAC;AACpC,MAAM,EAAE,GAAG,IAAA,wBAAY,GAAE,CAAC;AAE1B,2CAA2C;AAC3C,iCAAiC;AACjC,2CAA2C;AAE9B,QAAA,kBAAkB,GAAG,IAAA,6BAAiB,EACjD;IACE,QAAQ,EAAE,sBAAsB;IAChC,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,EAAE;CACnB,EACD,KAAK,EAAE,KAAK,EAAE,EAAE;;IACd,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,MAAA,KAAK,CAAC,IAAI,0CAAE,IAAI,EAAE,CAAC;QACvC,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC;QAEzC,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,WAAM,CAAC,KAAK,CAAC,uBAAuB,CAAC,CAAC;YACtC,OAAO;QACT,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,uCAAuC,SAAS,EAAE,EAAE,WAAW,CAAC,CAAC;QAE7E,2CAA2C;QAC3C,2BAA2B;QAC3B,2CAA2C;QAE3C,2DAA2D;QAC3D,MAAM,eAAe,GAAG,MAAM,EAAE;aAC7B,UAAU,CAAC,0BAA0B,CAAC;aACtC,KAAK,CAAC,yBAAyB,EAAE,IAAI,EAAE,IAAI,CAAC;aAC5C,KAAK,CAAC,QAAQ,EAAE,gBAAgB,EAAE,OAAO,CAAC;aAC1C,GAAG,EAAE,CAAC;QAET,MAAM,WAAW,GAAa,EAAE,CAAC;QACjC,eAAe,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE;YACjC,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;YACzB,IAAI,KAAK,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAClD,WAAW,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC;YACvC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,oCAAoC;YACpC,MAAM,iBAAiB,GAAG;gBACxB,YAAY,EAAE;oBACZ,KAAK,EAAE,6BAA6B;oBACpC,IAAI,EAAE,GAAG,WAAW,CAAC,IAAI,SAAS,WAAW,CAAC,OAAO,IAAI,iBAAiB,iBAAiB;oBAC3F,IAAI,EAAE,yBAAyB;oBAC/B,WAAW,EAAE,mBAAmB,SAAS,EAAE;iBAC5C;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,cAAc;oBACpB,SAAS;oBACT,WAAW,EAAE,WAAW,CAAC,IAAI;oBAC7B,YAAY,EAAE,WAAW,CAAC,KAAK;oBAC/B,cAAc,EAAE,WAAW,CAAC,OAAO,IAAI,EAAE;oBACzC,QAAQ,EAAE,WAAW,CAAC,QAAQ,IAAI,QAAQ;oBAC1C,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;iBACjC;aACF,CAAC;YAEF,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,YAAY,CAC/C,WAAW,EACX,iBAAiB,EACjB,MAAM,CACP,CAAC;YAEF,WAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,WAAW,CAAC,CAAC;QACzD,CAAC;QAED,2CAA2C;QAC3C,sBAAsB;QACtB,2CAA2C;QAE3C,6CAA6C;QAC7C,MAAM,iBAAiB,GAAG;YACxB,YAAY,EAAE;gBACZ,KAAK,EAAE,wBAAwB;gBAC/B,IAAI,EAAE,GAAG,WAAW,CAAC,IAAI,2BAA2B,WAAW,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK;gBAC9F,IAAI,EAAE,+BAA+B;gBACrC,WAAW,EAAE,mBAAmB,SAAS,EAAE;aAC5C;YACD,IAAI,EAAE;gBACJ,IAAI,EAAE,cAAc;gBACpB,SAAS;gBACT,WAAW,EAAE,WAAW,CAAC,IAAI;gBAC7B,YAAY,EAAE,WAAW,CAAC,KAAK;gBAC/B,MAAM,EAAE,WAAW,CAAC,MAAM,IAAI,SAAS;gBACvC,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;aACjC;SACF,CAAC;QAEF,MAAM,WAAW,GAAG,MAAM,UAAU,CAAC,WAAW,CAC9C,gBAAgB,EAChB,iBAAiB,EACjB,MAAM,CACP,CAAC;QAEF,WAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,WAAW,CAAC,CAAC;QAEvD,2CAA2C;QAC3C,uCAAuC;QACvC,2CAA2C;QAE3C,+CAA+C;QAC/C,MAAM,YAAY,GAAG,MAAM,EAAE;aAC1B,UAAU,CAAC,0BAA0B,CAAC;aACtC,KAAK,CAAC,OAAO,EAAE,IAAI,EAAE,WAAW,CAAC,KAAK,CAAC;aACvC,KAAK,CAAC,CAAC,CAAC;aACR,GAAG,EAAE,CAAC;QAET,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;YACxB,MAAM,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;YAE9C,IAAI,CAAA,MAAA,SAAS,CAAC,WAAW,0CAAE,WAAW,KAAI,CAAA,MAAA,SAAS,CAAC,SAAS,0CAAE,MAAM,IAAG,CAAC,EAAE,CAAC;gBAC1E,MAAM,wBAAwB,GAAG;oBAC/B,YAAY,EAAE;wBACZ,KAAK,EAAE,6BAA6B;wBACpC,IAAI,EAAE,qEAAqE;wBAC3E,IAAI,EAAE,8BAA8B;wBACpC,WAAW,EAAE,uBAAuB;qBACrC;oBACD,IAAI,EAAE;wBACJ,IAAI,EAAE,sBAAsB;wBAC5B,SAAS;wBACT,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;qBACjC;iBACF,CAAC;gBAEF,MAAM,kBAAkB,GAAG,MAAM,UAAU,CAAC,YAAY,CACtD,SAAS,CAAC,SAAS,EACnB,wBAAwB,EACxB,QAAQ,CACT,CAAC;gBAEF,WAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,kBAAkB,CAAC,CAAC;YAC/D,CAAC;QACH,CAAC;QAED,2CAA2C;QAC3C,0BAA0B;QAC1B,2CAA2C;QAE3C,qEAAqE;QACrE,MAAM,cAAc,GAAG,CAAC,QAAQ,EAAE,WAAW,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,CAAC,CAAC;QAClF,MAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAC7C,WAAW,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CACpD,IAAI,WAAW,CAAC,QAAQ,KAAK,QAAQ,CAAC;QAEvC,IAAI,QAAQ,EAAE,CAAC;YACb,2CAA2C;YAC3C,MAAM,kBAAkB,GAAG;gBACzB,YAAY,EAAE;oBACZ,KAAK,EAAE,wBAAwB;oBAC/B,IAAI,EAAE,WAAW,WAAW,CAAC,IAAI,8BAA8B;oBAC/D,IAAI,EAAE,wBAAwB;oBAC9B,WAAW,EAAE,mBAAmB,SAAS,cAAc;iBACxD;gBACD,IAAI,EAAE;oBACJ,IAAI,EAAE,gBAAgB;oBACtB,SAAS;oBACT,WAAW,EAAE,WAAW,CAAC,IAAI;oBAC7B,YAAY,EAAE,WAAW,CAAC,KAAK;oBAC/B,QAAQ,EAAE,QAAQ;oBAClB,SAAS,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,EAAE;oBAChC,kBAAkB,EAAE,MAAM;iBAC3B;aACF,CAAC;YAEF,MAAM,YAAY,GAAG,MAAM,UAAU,CAAC,WAAW,CAC/C,aAAa,EACb,kBAAkB,EAClB,QAAQ,CACT,CAAC;YAEF,WAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,YAAY,CAAC,CAAC;YAEzD,qDAAqD;YACrD,MAAM,EAAE,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;gBACpD,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE,IAAI;gBACd,sBAAsB,EAAE,IAAI;gBAC5B,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;QACL,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,kDAAkD,SAAS,EAAE,CAAC,CAAC;IAE7E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,WAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE,KAAK,CAAC,CAAC;QACnE,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CACF,CAAC"}