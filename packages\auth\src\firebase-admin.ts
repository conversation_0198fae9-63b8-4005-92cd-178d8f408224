/**
 * Firebase Admin SDK Configuration
 * Server-side only - for admin operations and token verification
 */

import { initializeApp, getApps, cert, App } from 'firebase-admin/app';
import { getAuth, Auth } from 'firebase-admin/auth';
import { getFirestore, Firestore } from 'firebase-admin/firestore';

// Server-side only - ensure this runs only on the server
if (typeof window !== 'undefined') {
  throw new Error('Firebase Admin SDK should only be used on the server side');
}

// Firebase Admin singleton
let adminApp: App | null = null;
let adminAuth: Auth | null = null;
let adminDb: Firestore | null = null;

// Initialize Firebase Admin
export function initializeFirebaseAdmin() {
  // Get environment variables (server-side only)
  const projectId = process.env.FIREBASE_ADMIN_PROJECT_ID;
  const privateKey = process.env.FIREBASE_ADMIN_PRIVATE_KEY;
  const clientEmail = process.env.FIREBASE_ADMIN_CLIENT_EMAIL;

  if (!projectId || !privateKey || !clientEmail) {
    throw new Error(
      'Firebase Admin credentials are missing. Please check your environment variables:\n' +
      '- FIREBASE_ADMIN_PROJECT_ID\n' +
      '- FIREBASE_ADMIN_PRIVATE_KEY\n' +
      '- FIREBASE_ADMIN_CLIENT_EMAIL'
    );
  }

  // Initialize app if not already initialized
  if (!adminApp && getApps().length === 0) {
    try {
      adminApp = initializeApp({
        credential: cert({
          projectId,
          privateKey: privateKey.replace(/\\n/g, '\n'), // Handle escaped newlines
          clientEmail,
        }),
        projectId,
      });
    } catch (error) {
      console.error('Failed to initialize Firebase Admin:', error);
      throw new Error('Failed to initialize Firebase Admin SDK');
    }
  } else if (getApps().length > 0) {
    adminApp = getApps()[0];
  }

  // Initialize services
  if (adminApp) {
    if (!adminAuth) {
      adminAuth = getAuth(adminApp);
    }
    if (!adminDb) {
      adminDb = getFirestore(adminApp);
    }
  }

  return {
    app: adminApp,
    auth: adminAuth,
    db: adminDb,
  };
}

// Lazy initialization - only initialize when needed
export function getFirebaseAdmin() {
  if (!adminApp) {
    return initializeFirebaseAdmin();
  }
  
  return {
    app: adminApp,
    auth: adminAuth!,
    db: adminDb!,
  };
}

// Individual service getters
export function getAdminAuth(): Auth {
  const { auth } = getFirebaseAdmin();
  if (!auth) {
    throw new Error('Firebase Admin Auth not initialized');
  }
  return auth;
}

export function getAdminDb(): Firestore {
  const { db } = getFirebaseAdmin();
  if (!db) {
    throw new Error('Firebase Admin Firestore not initialized');
  }
  return db;
}

// Export alias for compatibility
export const getAdminFirestore = getAdminDb;

// Helper functions for common admin operations
export async function verifyIdToken(idToken: string) {
  try {
    const auth = getAdminAuth();
    const decodedToken = await auth.verifyIdToken(idToken);
    return decodedToken;
  } catch (error) {
    console.error('Error verifying ID token:', error);
    throw new Error('Invalid ID token');
  }
}

// Verify admin user has proper claims
export async function verifyAdminUser(uid: string) {
  try {
    const auth = getAdminAuth();
    const user = await auth.getUser(uid);
    
    // Check if user has admin claims
    const customClaims = user.customClaims;
    if (!customClaims?.isAdmin) {
      throw new Error('User is not an admin');
    }
    
    return {
      uid: user.uid,
      email: user.email,
      emailVerified: user.emailVerified,
      customClaims,
    };
  } catch (error) {
    console.error('Error verifying admin user:', error);
    throw new Error('Failed to verify admin user');
  }
}

// Set custom user claims
export async function setCustomUserClaims(uid: string, customClaims: object) {
  try {
    const auth = getAdminAuth();
    await auth.setCustomUserClaims(uid, customClaims);
  } catch (error) {
    console.error('Error setting custom user claims:', error);
    throw new Error('Failed to set custom user claims');
  }
}

// Re-export types for convenience
export type {
  App as AdminApp,
  Auth as AdminAuth,
  Firestore as AdminFirestore,
};
