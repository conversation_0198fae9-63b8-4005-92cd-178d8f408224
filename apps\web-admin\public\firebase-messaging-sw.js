/**
 * Firebase Cloud Messaging Service Worker for Admin App
 * 
 * Handles background notifications, click actions, and admin-specific
 * notification features with enhanced priority handling.
 */

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/10.7.1/firebase-messaging-compat.js');

// ========================================
// FIREBASE CONFIGURATION
// ========================================

// Firebase configuration will be injected by the build process
// or loaded from environment variables
const firebaseConfig = {
  apiKey: "your_api_key",
  authDomain: "your_project.firebaseapp.com",
  projectId: "your_project_id",
  storageBucket: "your_project.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:abcdef123456"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging
const messaging = firebase.messaging();

// ========================================
// NOTIFICATION SETTINGS
// ========================================

const NOTIFICATION_SETTINGS = {
  // Default notification options
  DEFAULT_ICON: '/icons/admin-notification-icon.png',
  DEFAULT_BADGE: '/icons/admin-notification-badge.png',
  
  // Admin-specific settings
  URGENT_ICON: '/icons/urgent-notification-icon.png',
  URGENT_SOUND: '/sounds/urgent-alert.mp3',
  
  // Notification actions for admin
  ADMIN_ACTIONS: [
    {
      action: 'view',
      title: 'View Details',
      icon: '/icons/view-action.png'
    },
    {
      action: 'respond',
      title: 'Respond',
      icon: '/icons/respond-action.png'
    },
    {
      action: 'dismiss',
      title: 'Dismiss',
      icon: '/icons/dismiss-action.png'
    }
  ],
  
  // Priority-based settings
  PRIORITY_SETTINGS: {
    urgent: {
      requireInteraction: true,
      silent: false,
      vibrate: [200, 100, 200, 100, 200],
      actions: [
        { action: 'investigate', title: 'Investigate', icon: '/icons/investigate.png' },
        { action: 'assign', title: 'Assign', icon: '/icons/assign.png' },
        { action: 'dismiss', title: 'Dismiss', icon: '/icons/dismiss.png' }
      ]
    },
    high: {
      requireInteraction: true,
      silent: false,
      vibrate: [200, 100, 200],
      actions: [
        { action: 'view', title: 'View', icon: '/icons/view.png' },
        { action: 'respond', title: 'Respond', icon: '/icons/respond.png' }
      ]
    },
    normal: {
      requireInteraction: false,
      silent: false,
      actions: [
        { action: 'view', title: 'View', icon: '/icons/view.png' }
      ]
    },
    low: {
      requireInteraction: false,
      silent: true,
      actions: []
    }
  }
};

// ========================================
// BACKGROUND MESSAGE HANDLER
// ========================================

messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message:', payload);

  try {
    // Extract notification data
    const notificationTitle = payload.notification?.title || 'Admin Notification';
    const notificationOptions = createNotificationOptions(payload);

    // Show notification
    self.registration.showNotification(notificationTitle, notificationOptions);

    // Track notification analytics
    trackNotificationAnalytics('received', payload);

  } catch (error) {
    console.error('[firebase-messaging-sw.js] Error handling background message:', error);
  }
});

// ========================================
// NOTIFICATION CLICK HANDLER
// ========================================

self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification click received:', event);

  // Close the notification
  event.notification.close();

  const action = event.action;
  const data = event.notification.data || {};

  // Track click analytics
  trackNotificationAnalytics('clicked', { action, data });

  // Handle different actions
  event.waitUntil(
    handleNotificationAction(action, data)
  );
});

// ========================================
// NOTIFICATION CLOSE HANDLER
// ========================================

self.addEventListener('notificationclose', (event) => {
  console.log('[firebase-messaging-sw.js] Notification closed:', event);

  const data = event.notification.data || {};
  
  // Track dismissal analytics
  trackNotificationAnalytics('dismissed', { data });
});

// ========================================
// HELPER FUNCTIONS
// ========================================

/**
 * Create notification options based on payload and priority
 */
function createNotificationOptions(payload) {
  const notification = payload.notification || {};
  const data = payload.data || {};
  const priority = data.priority || 'normal';

  // Get priority-specific settings
  const prioritySettings = NOTIFICATION_SETTINGS.PRIORITY_SETTINGS[priority] || 
                          NOTIFICATION_SETTINGS.PRIORITY_SETTINGS.normal;

  // Base notification options
  const options = {
    body: notification.body || '',
    icon: getNotificationIcon(priority, notification.icon),
    badge: NOTIFICATION_SETTINGS.DEFAULT_BADGE,
    image: notification.image,
    data: {
      ...data,
      timestamp: Date.now(),
      priority
    },
    tag: data.type || 'admin-notification',
    renotify: priority === 'urgent',
    ...prioritySettings
  };

  // Add timestamp for admin notifications
  if (notification.body) {
    const timestamp = new Date().toLocaleTimeString();
    options.body = `${notification.body}\n\nReceived: ${timestamp}`;
  }

  return options;
}

/**
 * Get appropriate icon based on priority
 */
function getNotificationIcon(priority, customIcon) {
  if (customIcon) return customIcon;
  
  switch (priority) {
    case 'urgent':
      return NOTIFICATION_SETTINGS.URGENT_ICON;
    default:
      return NOTIFICATION_SETTINGS.DEFAULT_ICON;
  }
}

/**
 * Handle notification action clicks
 */
async function handleNotificationAction(action, data) {
  const baseUrl = self.location.origin;
  
  try {
    switch (action) {
      case 'view':
      case '': // Default click (no action)
        await openOrFocusWindow(`${baseUrl}/admin/notifications/${data.notificationId || ''}`);
        break;
        
      case 'respond':
        await openOrFocusWindow(`${baseUrl}/admin/respond/${data.contactId || data.id || ''}`);
        break;
        
      case 'investigate':
        await openOrFocusWindow(`${baseUrl}/admin/investigate/${data.alertId || data.id || ''}`);
        break;
        
      case 'assign':
        await openOrFocusWindow(`${baseUrl}/admin/assign/${data.taskId || data.id || ''}`);
        break;
        
      case 'dismiss':
        // Just track the dismissal, no window opening needed
        console.log('Notification dismissed via action');
        break;
        
      default:
        // Default action - open admin dashboard
        await openOrFocusWindow(`${baseUrl}/admin`);
    }
  } catch (error) {
    console.error('Error handling notification action:', error);
    // Fallback to opening admin dashboard
    await openOrFocusWindow(`${baseUrl}/admin`);
  }
}

/**
 * Open or focus existing window
 */
async function openOrFocusWindow(url) {
  try {
    // Try to find existing window with the same URL
    const windowClients = await clients.matchAll({
      type: 'window',
      includeUncontrolled: true
    });

    // Check if we already have this URL open
    for (const client of windowClients) {
      if (client.url === url && 'focus' in client) {
        return client.focus();
      }
    }

    // Check if we have any admin window open
    for (const client of windowClients) {
      if (client.url.includes('/admin') && 'navigate' in client) {
        client.navigate(url);
        return client.focus();
      }
    }

    // Open new window
    return clients.openWindow(url);
  } catch (error) {
    console.error('Error opening/focusing window:', error);
    return clients.openWindow(url);
  }
}

/**
 * Track notification analytics
 */
function trackNotificationAnalytics(action, payload) {
  try {
    // Send analytics to your backend
    const analyticsData = {
      action,
      timestamp: Date.now(),
      userAgent: navigator.userAgent,
      payload: payload
    };

    // You can send this to your analytics endpoint
    // fetch('/api/analytics/notifications', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(analyticsData)
    // }).catch(console.error);

    console.log('[Analytics] Notification event:', analyticsData);
  } catch (error) {
    console.error('Error tracking notification analytics:', error);
  }
}

// ========================================
// SERVICE WORKER LIFECYCLE
// ========================================

self.addEventListener('install', (event) => {
  console.log('[firebase-messaging-sw.js] Service worker installing...');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('[firebase-messaging-sw.js] Service worker activating...');
  event.waitUntil(clients.claim());
});

// ========================================
// ERROR HANDLING
// ========================================

self.addEventListener('error', (event) => {
  console.error('[firebase-messaging-sw.js] Service worker error:', event.error);
});

self.addEventListener('unhandledrejection', (event) => {
  console.error('[firebase-messaging-sw.js] Unhandled promise rejection:', event.reason);
});
