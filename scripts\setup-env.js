#!/usr/bin/env node

/**
 * Environment Setup Script for Encreasl Monorepo
 * 
 * This script helps set up environment variables for the monorepo
 * by copying example files and providing guidance.
 */

const fs = require('fs');
const path = require('path');

const envFiles = [
  {
    example: '.env.example',
    target: '.env.local',
    description: 'Root shared environment variables'
  },
  {
    example: 'apps/web/.env.example',
    target: 'apps/web/.env.local',
    description: 'Web app specific variables'
  },
  {
    example: 'apps/web-admin/.env.example',
    target: 'apps/web-admin/.env.local',
    description: 'Admin app specific variables'
  }
];

function copyEnvFile(example, target, description) {
  const examplePath = path.resolve(example);
  const targetPath = path.resolve(target);
  
  if (!fs.existsSync(examplePath)) {
    console.log(`❌ Example file not found: ${example}`);
    return false;
  }
  
  if (fs.existsSync(targetPath)) {
    console.log(`⚠️  Target file already exists: ${target}`);
    console.log(`   Skipping to avoid overwriting existing configuration.`);
    return true;
  }
  
  try {
    fs.copyFileSync(examplePath, targetPath);
    console.log(`✅ Created: ${target}`);
    console.log(`   ${description}`);
    return true;
  } catch (error) {
    console.log(`❌ Failed to create: ${target}`);
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

function main() {
  console.log('🌍 Encreasl Environment Setup');
  console.log('==============================\n');
  
  let allSuccess = true;
  
  for (const { example, target, description } of envFiles) {
    const success = copyEnvFile(example, target, description);
    allSuccess = allSuccess && success;
    console.log('');
  }
  
  if (allSuccess) {
    console.log('🎉 Environment setup completed successfully!\n');
    console.log('📝 Next steps:');
    console.log('   1. Edit .env.local with your Firebase configuration');
    console.log('   2. Edit apps/web/.env.local with web-specific settings');
    console.log('   3. Edit apps/web-admin/.env.local with admin-specific settings');
    console.log('   4. Run: pnpm install');
    console.log('   5. Run: pnpm turbo dev\n');
    console.log('📖 For detailed guidance, see: docs/environment-variables.md');
  } else {
    console.log('⚠️  Some environment files could not be set up.');
    console.log('   Please check the errors above and try again.');
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { copyEnvFile, envFiles };
