/**
 * FCM React Hooks
 * 
 * React hooks for Firebase Cloud Messaging functionality
 * including permission management, token handling, and notifications.
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import { getMessaging, getToken, onMessage, MessagePayload } from 'firebase/messaging';
import { initializeApp, getApps } from 'firebase/app';
import type { 
  FCMHookState, 
  NotificationPermissionState, 
  FCMConfig,
  NotificationPreferences 
} from '../types';
import { FCM_SETTINGS } from '../config';

// ========================================
// MAIN FCM HOOK
// ========================================

/**
 * Main FCM hook for managing Firebase Cloud Messaging
 */
export function useFCM(config: FCMConfig) {
  const [state, setState] = useState<FCMHookState>({
    isInitialized: false,
    isSupported: false,
    permission: null,
    token: null,
    error: null,
    isLoading: true,
  });

  const messagingRef = useRef<any>(null);
  const unsubscribeRef = useRef<(() => void) | null>(null);

  // Initialize Firebase and FCM
  const initialize = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      // Check if running in browser
      if (typeof window === 'undefined') {
        setState(prev => ({ 
          ...prev, 
          isSupported: false, 
          isLoading: false,
          error: 'FCM is not supported in server-side environment'
        }));
        return;
      }

      // Check if notifications are supported
      if (!('Notification' in window) || !('serviceWorker' in navigator)) {
        setState(prev => ({ 
          ...prev, 
          isSupported: false, 
          isLoading: false,
          error: 'Notifications or Service Workers not supported'
        }));
        return;
      }

      // Initialize Firebase app if not already initialized
      let app;
      const existingApps = getApps();
      if (existingApps.length > 0) {
        app = existingApps[0];
      } else {
        app = initializeApp(config);
      }

      // Initialize messaging
      const messaging = getMessaging(app);
      messagingRef.current = messaging;

      // Register service worker
      if ('serviceWorker' in navigator) {
        try {
          await navigator.serviceWorker.register(FCM_SETTINGS.SERVICE_WORKER_PATH, {
            scope: FCM_SETTINGS.SERVICE_WORKER_SCOPE,
          });
        } catch (swError) {
          console.warn('Service worker registration failed:', swError);
        }
      }

      // Get current permission status
      const permission = Notification.permission;

      setState(prev => ({
        ...prev,
        isInitialized: true,
        isSupported: true,
        permission,
        isLoading: false,
      }));

      // Set up message listener
      const unsubscribe = onMessage(messaging, (payload: MessagePayload) => {
        console.log('Foreground message received:', payload);
        
        // Handle foreground notifications
        if (payload.notification) {
          showNotification(payload.notification);
        }
      });

      unsubscribeRef.current = unsubscribe;

    } catch (error) {
      console.error('FCM initialization error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to initialize FCM',
        isLoading: false,
      }));
    }
  }, [config]);

  // Request notification permission
  const requestPermission = useCallback(async (): Promise<boolean> => {
    try {
      if (!state.isSupported) {
        throw new Error('Notifications not supported');
      }

      const permission = await Notification.requestPermission();
      setState(prev => ({ ...prev, permission }));

      return permission === 'granted';
    } catch (error) {
      console.error('Permission request error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to request permission',
      }));
      return false;
    }
  }, [state.isSupported]);

  // Get FCM token
  const getTokenAsync = useCallback(async (vapidKey?: string): Promise<string | null> => {
    try {
      if (!messagingRef.current || state.permission !== 'granted') {
        return null;
      }

      const token = await getToken(messagingRef.current, {
        vapidKey: vapidKey || config.vapidKey,
      });

      setState(prev => ({ ...prev, token }));
      return token;
    } catch (error) {
      console.error('Token retrieval error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to get token',
      }));
      return null;
    }
  }, [state.permission, config.vapidKey]);

  // Subscribe to topic
  const subscribeToTopic = useCallback(async (topic: string): Promise<boolean> => {
    try {
      const token = state.token || await getTokenAsync();
      if (!token) {
        throw new Error('No FCM token available');
      }

      // Call your backend API to subscribe to topic
      const response = await fetch('/api/fcm/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, topic }),
      });

      if (!response.ok) {
        throw new Error('Failed to subscribe to topic');
      }

      return true;
    } catch (error) {
      console.error('Topic subscription error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to subscribe to topic',
      }));
      return false;
    }
  }, [state.token, getTokenAsync]);

  // Unsubscribe from topic
  const unsubscribeFromTopic = useCallback(async (topic: string): Promise<boolean> => {
    try {
      const token = state.token || await getTokenAsync();
      if (!token) {
        throw new Error('No FCM token available');
      }

      // Call your backend API to unsubscribe from topic
      const response = await fetch('/api/fcm/unsubscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ token, topic }),
      });

      if (!response.ok) {
        throw new Error('Failed to unsubscribe from topic');
      }

      return true;
    } catch (error) {
      console.error('Topic unsubscription error:', error);
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to unsubscribe from topic',
      }));
      return false;
    }
  }, [state.token, getTokenAsync]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current();
      }
    };
  }, []);

  return {
    ...state,
    initialize,
    requestPermission,
    getToken: getTokenAsync,
    subscribeToTopic,
    unsubscribeFromTopic,
  };
}

// ========================================
// NOTIFICATION PERMISSION HOOK
// ========================================

/**
 * Hook for managing notification permissions
 */
export function useNotificationPermission() {
  const [state, setState] = useState<NotificationPermissionState>({
    permission: null,
    isSupported: false,
    isLoading: true,
    error: null,
  });

  useEffect(() => {
    // Check if running in browser
    if (typeof window === 'undefined') {
      setState({
        permission: null,
        isSupported: false,
        isLoading: false,
        error: 'Not supported in server-side environment',
      });
      return;
    }

    // Check if notifications are supported
    if (!('Notification' in window)) {
      setState({
        permission: null,
        isSupported: false,
        isLoading: false,
        error: 'Notifications not supported',
      });
      return;
    }

    setState({
      permission: Notification.permission,
      isSupported: true,
      isLoading: false,
      error: null,
    });
  }, []);

  const requestPermission = useCallback(async (): Promise<boolean> => {
    try {
      if (!state.isSupported) {
        throw new Error('Notifications not supported');
      }

      setState(prev => ({ ...prev, isLoading: true, error: null }));

      const permission = await Notification.requestPermission();
      
      setState(prev => ({ 
        ...prev, 
        permission, 
        isLoading: false 
      }));

      return permission === 'granted';
    } catch (error) {
      setState(prev => ({
        ...prev,
        error: error instanceof Error ? error.message : 'Failed to request permission',
        isLoading: false,
      }));
      return false;
    }
  }, [state.isSupported]);

  return {
    ...state,
    requestPermission,
  };
}

// ========================================
// NOTIFICATION PREFERENCES HOOK
// ========================================

/**
 * Hook for managing user notification preferences
 */
export function useNotificationPreferences() {
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load preferences
  const loadPreferences = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);

      const response = await fetch('/api/fcm/preferences');
      if (!response.ok) {
        throw new Error('Failed to load preferences');
      }

      const data = await response.json();
      setPreferences(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load preferences');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update preferences
  const updatePreferences = useCallback(async (updates: Partial<NotificationPreferences>) => {
    try {
      setError(null);

      const response = await fetch('/api/fcm/preferences', {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updates),
      });

      if (!response.ok) {
        throw new Error('Failed to update preferences');
      }

      const updatedPreferences = await response.json();
      setPreferences(updatedPreferences);
      
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update preferences');
      return false;
    }
  }, []);

  // Load preferences on mount
  useEffect(() => {
    loadPreferences();
  }, [loadPreferences]);

  return {
    preferences,
    isLoading,
    error,
    loadPreferences,
    updatePreferences,
  };
}

// ========================================
// HELPER FUNCTIONS
// ========================================

/**
 * Show a browser notification
 */
function showNotification(notification: any) {
  if (Notification.permission === 'granted') {
    new Notification(notification.title, {
      body: notification.body,
      icon: notification.icon || FCM_SETTINGS.DEFAULT_NOTIFICATION_ICON,
      badge: notification.badge || FCM_SETTINGS.DEFAULT_NOTIFICATION_BADGE,
      tag: notification.tag,
      data: notification.data,
    });
  }
}
