For "roles" collection:

// Firestore Database Structure
// Collection: roles
// Document ID: admin

const rolesSchema = {
  // Collection name
  collectionName: "roles",
  
  // Document structure for role documents
  documentStructure: {
    // Document fields
    canManageContent: "boolean",     // true/false
    canManageRoles: "boolean",       // true/false  
    canManageSettings: "boolean",    // true/false
    canManageUsers: "boolean",       // true/false
    createdAt: "timestamp",          // Firestore timestamp
    description: "string",           // Role description
    id: "string",                    // Role identifier (also used as document ID)
    isActive: "boolean",             // true/false
    isSystemRole: "boolean",         // true/false
    level: "number",                 // Numeric level (e.g., 100)
    name: "string",                  // Display name
    permissions: "array",            // Array of strings
    updatedAt: "timestamp",          // Firestore timestamp
    userCount: "number"              // Count of users with this role
  }
};

// Example document data for the "admin" role
const adminRoleDocument = {
  canManageContent: true,
  canManageRoles: true,
  canManageSettings: true,
  canManageUsers: true,
  createdAt: new Date("2025-07-21T10:22:30.000Z"), // Adjusted from UTC+8
  description: "Full access to all features (like WordPress admin)",
  id: "admin",
  isActive: true,
  isSystemRole: true,
  level: 100,
  name: "Administrator",
  permissions: ["*"],
  updatedAt: new Date("2025-07-21T10:22:30.000Z"), // Adjusted from UTC+8
  userCount: 2
};

// Firestore collection path structure:
// /roles/{roleId}
// 
// Where {roleId} would be "admin", "editor", etc.

// Example Firestore rules for this collection
const firestoreRules = `
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Roles collection - restrict access based on user permissions
    match /roles/{roleId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
                      resource.data.canManageRoles == true;
    }
  }
}
`;

// Example queries you might use:
const exampleQueries = {
  // Get all active roles
  getActiveRoles: `
    db.collection('roles')
      .where('isActive', '==', true)
      .orderBy('level', 'desc')
  `,
  
  // Get system roles
  getSystemRoles: `
    db.collection('roles')
      .where('isSystemRole', '==', true)
  `,
  
  // Get roles with specific permission
  getRolesWithPermission: `
    db.collection('roles')
      .where('permissions', 'array-contains', '*')
  `
};

export { rolesSchema, adminRoleDocument, firestoreRules, exampleQueries };