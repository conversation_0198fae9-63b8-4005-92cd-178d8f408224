/**
 * Firebase Configuration Utility
 * Based on Firebase v10 official documentation
 */

import { initializeApp, FirebaseApp, getApps } from 'firebase/app';
import { getAuth, Auth, connectAuthEmulator } from 'firebase/auth';
import { FirebaseConfig } from './types';

// ========================================
// FIREBASE APP INITIALIZATION
// ========================================

let firebaseApp: FirebaseApp | null = null;
let firebaseAuth: Auth | null = null;

/**
 * Initialize Firebase app with configuration
 * Following Firebase v10 best practices
 */
export function initializeFirebase(config: FirebaseConfig): FirebaseApp {
  // Prevent multiple initializations
  if (firebaseApp) {
    return firebaseApp;
  }

  // Check if app is already initialized
  const existingApps = getApps();
  if (existingApps.length > 0) {
    firebaseApp = existingApps[0];
    return firebaseApp;
  }

  // Initialize Firebase app
  firebaseApp = initializeApp({
    apiKey: config.apiKey,
    authDomain: config.authDomain,
    projectId: config.projectId,
    storageBucket: config.storageBucket,
    messagingSenderId: config.messagingSenderId,
    appId: config.appId,
  });

  return firebaseApp;
}

/**
 * Get Firebase Auth instance
 * Following Firebase v10 best practices
 */
export function getFirebaseAuth(app?: FirebaseApp): Auth {
  if (firebaseAuth) {
    return firebaseAuth;
  }

  const appInstance = app || firebaseApp;
  if (!appInstance) {
    // Try to auto-initialize with environment variables
    try {
      const config = createFirebaseConfigFromEnv();
      const initializedApp = initializeFirebase(config);
      firebaseAuth = getAuth(initializedApp);
      return firebaseAuth;
    } catch (error) {
      throw new Error('Firebase app not initialized. Call initializeFirebase() first or ensure environment variables are set.');
    }
  }

  firebaseAuth = getAuth(appInstance);

  // Connect to Auth emulator in development (DISABLED - using production auth)
  // if (process.env.NODE_ENV === 'development' && !firebaseAuth.config.emulator) {
  //   try {
  //     connectAuthEmulator(firebaseAuth, 'http://localhost:9099', { disableWarnings: true });
  //   } catch (error) {
  //     // Emulator connection failed, continue with production auth
  //     console.warn('Failed to connect to Auth emulator, using production auth');
  //   }
  // }

  return firebaseAuth;
}

/**
 * Create Firebase config from environment variables
 */
export function createFirebaseConfig(env: Record<string, string | undefined>): FirebaseConfig {
  const requiredFields = [
    'NEXT_PUBLIC_FIREBASE_API_KEY',
    'NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN',
    'NEXT_PUBLIC_FIREBASE_PROJECT_ID',
    'NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET',
    'NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID',
    'NEXT_PUBLIC_FIREBASE_APP_ID',
  ];

  // Validate required fields
  for (const field of requiredFields) {
    if (!env[field]) {
      throw new Error(`Missing required Firebase environment variable: ${field}`);
    }
  }

  return {
    apiKey: env.NEXT_PUBLIC_FIREBASE_API_KEY!,
    authDomain: env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN!,
    projectId: env.NEXT_PUBLIC_FIREBASE_PROJECT_ID!,
    storageBucket: env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET!,
    messagingSenderId: env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID!,
    appId: env.NEXT_PUBLIC_FIREBASE_APP_ID!,
    vapidKey: env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
  };
}

/**
 * Validate Firebase configuration
 */
export function validateFirebaseConfig(config: FirebaseConfig): boolean {
  const requiredFields: (keyof FirebaseConfig)[] = [
    'apiKey',
    'authDomain', 
    'projectId',
    'storageBucket',
    'messagingSenderId',
    'appId'
  ];

  for (const field of requiredFields) {
    if (!config[field] || typeof config[field] !== 'string' || config[field].trim() === '') {
      console.error(`Invalid Firebase config: ${field} is required and must be a non-empty string`);
      console.error(`Current value for ${field}:`, config[field]);
      console.error('Full config object:', config);
      return false;
    }
  }

  // Validate API key format (should start with AIza)
  if (!config.apiKey.startsWith('AIza')) {
    console.error('Invalid Firebase API key format');
    return false;
  }

  // Validate auth domain format
  if (!config.authDomain.includes('.firebaseapp.com') && !config.authDomain.includes('.web.app')) {
    console.error('Invalid Firebase auth domain format');
    return false;
  }

  return true;
}

/**
 * Get current Firebase app instance
 */
export function getCurrentFirebaseApp(): FirebaseApp | null {
  return firebaseApp;
}

/**
 * Get current Firebase Auth instance
 */
export function getCurrentFirebaseAuth(): Auth | null {
  return firebaseAuth;
}

// ========================================
// ADMIN-SPECIFIC CONFIGURATION
// ========================================

/**
 * Create Firebase configuration from environment variables
 * Supports both regular and admin app configurations
 */
export function createFirebaseConfigFromEnv(isAdmin = false): FirebaseConfig {
  // All Firebase client config uses NEXT_PUBLIC_ prefix regardless of app type
  const config: FirebaseConfig = {
    apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY || '',
    authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || '',
    projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || '',
    storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || '',
    messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '',
    appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID || '',
    vapidKey: process.env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
  };

  // Debug logging for troubleshooting
  if (process.env.NODE_ENV === 'development') {
    console.log('🔧 Firebase config debug:', {
      hasApiKey: !!config.apiKey,
      hasAuthDomain: !!config.authDomain,
      hasProjectId: !!config.projectId,
      hasStorageBucket: !!config.storageBucket,
      hasMessagingSenderId: !!config.messagingSenderId,
      hasAppId: !!config.appId,
      hasVapidKey: !!config.vapidKey,
    });
  }

  if (!validateFirebaseConfig(config)) {
    throw new Error(`Invalid Firebase configuration for ${isAdmin ? 'admin' : 'regular'} app`);
  }

  return config;
}

/**
 * Initialize Firebase for admin applications
 * Includes additional security checks and admin-specific configuration
 */
export function initializeAdminFirebase(config?: FirebaseConfig): FirebaseApp {
  const adminConfig = config || createFirebaseConfigFromEnv(true);

  // Additional validation for admin apps
  if (!adminConfig.projectId) {
    throw new Error('Project ID is required for admin Firebase initialization');
  }

  // Initialize with admin-specific settings
  const app = initializeFirebase(adminConfig);

  // Set up admin-specific auth configuration
  const auth = getFirebaseAuth();

  // Enable additional security features for admin
  if (process.env.NODE_ENV === 'development') {
    console.log('🔐 Admin Firebase initialized with enhanced security');
  }

  return app;
}

/**
 * Validate admin-specific Firebase configuration
 */
export function validateAdminFirebaseConfig(config: FirebaseConfig): boolean {
  // Run standard validation first
  if (!validateFirebaseConfig(config)) {
    return false;
  }

  // Additional admin-specific validations
  if (!config.projectId) {
    console.error('Admin Firebase config: Project ID is required');
    return false;
  }

  // Ensure we're using the correct project for admin
  const expectedProjectPattern = /^[a-z0-9-]+$/;
  if (!expectedProjectPattern.test(config.projectId)) {
    console.error('Admin Firebase config: Invalid project ID format');
    return false;
  }

  return true;
}
