/**
 * FCM React Hooks
 *
 * React hooks for Firebase Cloud Messaging functionality
 * including permission management, token handling, and notifications.
 */
import type { FCMConfig, NotificationPreferences } from '../types';
/**
 * Main FCM hook for managing Firebase Cloud Messaging
 */
export declare function useFCM(config: FCMConfig): {
    initialize: () => Promise<void>;
    requestPermission: () => Promise<boolean>;
    getToken: (vapidKey?: string) => Promise<string | null>;
    subscribeToTopic: (topic: string) => Promise<boolean>;
    unsubscribeFromTopic: (topic: string) => Promise<boolean>;
    isInitialized: boolean;
    isSupported: boolean;
    permission: NotificationPermission | null;
    token: string | null;
    error: string | null;
    isLoading: boolean;
};
/**
 * Hook for managing notification permissions
 */
export declare function useNotificationPermission(): {
    requestPermission: () => Promise<boolean>;
    permission: NotificationPermission | null;
    isSupported: boolean;
    isLoading: boolean;
    error: string | null;
};
/**
 * Hook for managing user notification preferences
 */
export declare function useNotificationPreferences(): {
    preferences: NotificationPreferences | null;
    isLoading: boolean;
    error: string | null;
    loadPreferences: () => Promise<void>;
    updatePreferences: (updates: Partial<NotificationPreferences>) => Promise<boolean>;
};
//# sourceMappingURL=hooks.d.ts.map