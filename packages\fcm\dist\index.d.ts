/**
 * @encreasl/fcm - Shared Firebase Cloud Messaging Package
 *
 * Provides FCM utilities, configurations, and React hooks
 * for the Encreasl monorepo. Supports both client and server-side usage.
 */
export * from './types';
export * from './client';
export * from './server';
export * from './utils';
export * from './config';
export type { FCMConfig, FCMMessage, NotificationPayload, NotificationData, NotificationPriority, NotificationType, FCMDevice, FCMTopic, NotificationPreferences, } from './types';
export { useFCM, useNotificationPermission, useNotificationPreferences, } from './client/hooks';
export { validateFCMConfig, createFCMMessage, formatNotificationData, } from './utils';
export { createFCMConfig, getFCMConfig, validateFCMEnvironment, } from './config';
//# sourceMappingURL=index.d.ts.map