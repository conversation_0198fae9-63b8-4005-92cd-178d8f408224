For "users" collection:

// Firestore Database Structure
// Collection: roles
// Document ID: admin

const rolesSchema = {
  // Collection name
  collectionName: "roles",
  
  // Document structure for role documents
  documentStructure: {
    // Document fields
    canManageContent: "boolean",     // true/false
    canManageRoles: "boolean",       // true/false  
    canManageSettings: "boolean",    // true/false
    canManageUsers: "boolean",       // true/false
    createdAt: "timestamp",          // Firestore timestamp
    description: "string",           // Role description
    id: "string",                    // Role identifier (also used as document ID)
    isActive: "boolean",             // true/false
    isSystemRole: "boolean",         // true/false
    level: "number",                 // Numeric level (e.g., 100)
    name: "string",                  // Display name
    permissions: "array",            // Array of strings
    updatedAt: "timestamp",          // Firestore timestamp
    userCount: "number"              // Count of users with this role
  }
};

// Example document data for the "admin" role
const adminRoleDocument = {
  canManageContent: true,
  canManageRoles: true,
  canManageSettings: true,
  canManageUsers: true,
  createdAt: new Date("2025-07-21T10:22:30.000Z"), // Adjusted from UTC+8
  description: "Full access to all features (like WordPress admin)",
  id: "admin",
  isActive: true,
  isSystemRole: true,
  level: 100,
  name: "Administrator",
  permissions: ["*"],
  updatedAt: new Date("2025-07-21T10:22:30.000Z"), // Adjusted from UTC+8
  userCount: 2
};

// Firestore collection path structure:
// /roles/{roleId}
// 
// Where {roleId} would be "admin", "editor", etc.

// Example Firestore rules for this collection
const firestoreRules = `
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Roles collection - restrict access based on user permissions
    match /roles/{roleId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
                      resource.data.canManageRoles == true;
    }
  }
}
`;

// Example queries you might use:
const exampleQueries = {
  // Get all active roles
  getActiveRoles: `
    db.collection('roles')
      .where('isActive', '==', true)
      .orderBy('level', 'desc')
  `,
  
  // Get system roles
  getSystemRoles: `
    db.collection('roles')
      .where('isSystemRole', '==', true)
  `,
  
  // Get roles with specific permission
  getRolesWithPermission: `
    db.collection('roles')
      .where('permissions', 'array-contains', '*')
  `
};

// Users Collection Schema
const usersSchema = {
  // Collection name
  collectionName: "users",
  
  // Document structure for user documents
  documentStructure: {
    // Basic user info
    accountType: "string",           // e.g., "free", "premium", "enterprise"
    authProvider: "string",          // e.g., "email", "google", "facebook"
    bio: "string",                   // User biography/description
    createdAt: "timestamp",          // Account creation timestamp
    createdBy: "string",             // Who created the account (e.g., "system", userId)
    displayName: "string",           // User's display name
    email: "string",                 // Email address
    emailVerified: "boolean",        // Email verification status
    failedLoginAttempts: "number",   // Security tracking
    firstName: "string",             // First name
    id: "string",                    // User identifier (also document ID)
    isActive: "boolean",             // Account active status
    isDeleted: "boolean",            // Soft delete flag
    isVerified: "boolean",           // Overall verification status
    lastActiveAt: "timestamp|null",  // Last activity timestamp
    lastLoginAt: "timestamp|null",   // Last login timestamp
    lastName: "string",              // Last name
    loginCount: "number",            // Total login count
    phoneVerified: "boolean",        // Phone verification status
    updatedAt: "timestamp",          // Last update timestamp
    updatedBy: "string",             // Who last updated (userId or "system")
    version: "number",               // Document version for optimistic locking
    
    // Nested objects (maps)
    analytics: {
      firstVisitAt: "timestamp",     // First visit timestamp
      signupSource: "string",        // e.g., "mobile", "web", "referral"
      totalSessions: "number",       // Total session count
      totalTimeSpent: "number"       // Total time spent (in seconds/minutes)
    },
    
    compliance: {
      ccpaCompliant: "boolean",      // CCPA compliance status
      gdprCompliant: "boolean",      // GDPR compliance status
      privacyPolicyAcceptedAt: "timestamp", // Privacy policy acceptance
      termsAcceptedAt: "timestamp"   // Terms acceptance timestamp
    },
    
    preferences: {
      currency: "string",            // Preferred currency (e.g., "USD")
      dateFormat: "string",          // Date format preference
      language: "string",            // Language code (e.g., "en")
      theme: "string",               // UI theme (e.g., "light", "dark")
      timeFormat: "string",          // Time format (e.g., "12h", "24h")
      timezone: "string",            // Timezone (e.g., "UTC", "America/New_York")
      
      notifications: {
        email: "boolean",            // Email notifications enabled
        marketing: "boolean",        // Marketing notifications enabled
        push: "boolean",             // Push notifications enabled
        sms: "boolean"               // SMS notifications enabled
      },
      
      privacy: {
        allowSearchByEmail: "boolean",   // Allow email-based search
        profileVisibility: "string",     // e.g., "public", "private", "friends"
        showEmail: "boolean",            // Show email in profile
        showPhone: "boolean"             // Show phone in profile
      }
    }
  }
};

// Example document data for demo user
const demoUserDocument = {
  accountType: "free",
  authProvider: "email",
  bio: "This is a sample user created to initialize the users collection.",
  createdAt: new Date("2025-07-21T17:03:30.000Z"), // Adjusted from UTC+8
  createdBy: "system",
  displayName: "Demo User Two",
  email: "<EMAIL>",
  emailVerified: false,
  failedLoginAttempts: 0,
  firstName: "Demo",
  id: "demo-user-2-*************",
  isActive: true,
  isDeleted: false,
  isVerified: false,
  lastActiveAt: null,
  lastLoginAt: null,
  lastName: "User Two",
  loginCount: 0,
  phoneVerified: false,
  updatedAt: new Date("2025-07-21T17:03:30.000Z"), // Adjusted from UTC+8
  updatedBy: "system",
  version: 1,
  
  analytics: {
    firstVisitAt: new Date("2025-07-21T17:03:30.000Z"),
    signupSource: "mobile",
    totalSessions: 0,
    totalTimeSpent: 0
  },
  
  compliance: {
    ccpaCompliant: true,
    gdprCompliant: true,
    privacyPolicyAcceptedAt: new Date("2025-07-21T17:03:30.000Z"),
    termsAcceptedAt: new Date("2025-07-21T17:03:30.000Z")
  },
  
  preferences: {
    currency: "USD",
    dateFormat: "MM/DD/YYYY",
    language: "en",
    theme: "light",
    timeFormat: "12h",
    timezone: "UTC",
    
    notifications: {
      email: true,
      marketing: false,
      push: true,
      sms: false
    },
    
    privacy: {
      allowSearchByEmail: true,
      profileVisibility: "public",
      showEmail: false,
      showPhone: false
    }
  }
};

// Updated Firestore rules for both collections
const completeFirestoreRules = `
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Roles collection - restrict access based on user permissions
    match /roles/{roleId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
                      hasManageRolesPermission(request.auth.uid);
    }
    
    // Users collection - users can read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && 
                           request.auth.uid == userId;
      // Admins can read/write all user data
      allow read, write: if request.auth != null && 
                           hasManageUsersPermission(request.auth.uid);
    }
    
    // Helper function to check role permissions
    function hasManageRolesPermission(uid) {
      return exists(/databases/$(database)/documents/users/$(uid)) &&
             get(/databases/$(database)/documents/users/$(uid)).data.permissions.hasAny(['*', 'manage_roles']);
    }
    
    function hasManageUsersPermission(uid) {
      return exists(/databases/$(database)/documents/users/$(uid)) &&
             get(/databases/$(database)/documents/users/$(uid)).data.permissions.hasAny(['*', 'manage_users']);
    }
  }
}
`;

// Example queries for users collection
const userQueries = {
  // Get user by email
  getUserByEmail: `
    db.collection('users')
      .where('email', '==', '<EMAIL>')
      .where('isDeleted', '==', false)
      .limit(1)
  `,
  
  // Get active users
  getActiveUsers: `
    db.collection('users')
      .where('isActive', '==', true)
      .where('isDeleted', '==', false)
      .orderBy('createdAt', 'desc')
  `,
  
  // Get users by account type
  getUsersByAccountType: `
    db.collection('users')
      .where('accountType', '==', 'premium')
      .where('isDeleted', '==', false)
  `,
  
  // Get users who need email verification
  getUnverifiedUsers: `
    db.collection('users')
      .where('emailVerified', '==', false)
      .where('isActive', '==', true)
      .where('isDeleted', '==', false)
  `
};

export { 
  rolesSchema, 
  adminRoleDocument, 
  usersSchema, 
  demoUserDocument,
  completeFirestoreRules, 
  exampleQueries,
  userQueries 
};