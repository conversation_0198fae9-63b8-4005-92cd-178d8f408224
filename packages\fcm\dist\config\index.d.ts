/**
 * FCM Configuration Utilities
 *
 * Shared configuration utilities for Firebase Cloud Messaging
 * that work across different environments and applications.
 */
import type { FCMConfig, FCMValidationResult } from '../types';
/**
 * Create FCM configuration from environment variables
 */
export declare function createFCMConfig(env: Record<string, string | undefined>): FCMConfig;
/**
 * Get FCM configuration from process.env
 */
export declare function getFCMConfig(): FCMConfig;
/**
 * Validate FCM configuration
 */
export declare function validateFCMConfig(config: Partial<FCMConfig>): FCMValidationResult;
/**
 * Validate FCM environment variables
 */
export declare function validateFCMEnvironment(env?: Record<string, string | undefined>): FCMValidationResult;
/**
 * Check if FCM is properly configured in the current environment
 */
export declare function isFCMConfigured(env?: Record<string, string | undefined>): boolean;
/**
 * Get FCM configuration status with detailed information
 */
export declare function getFCMConfigStatus(env?: Record<string, string | undefined>): {
    isConfigured: boolean;
    hasVapidKey: boolean;
    projectId: string | null;
    errors: string[];
    warnings: string[];
};
/**
 * Default FCM configuration for development
 */
export declare const DEFAULT_FCM_CONFIG: Partial<FCMConfig>;
/**
 * FCM feature flags and settings
 */
export declare const FCM_SETTINGS: {
    readonly ENABLE_BACKGROUND_MESSAGING: true;
    readonly ENABLE_NOTIFICATION_ANALYTICS: true;
    readonly ENABLE_TOPIC_SUBSCRIPTIONS: true;
    readonly DEFAULT_NOTIFICATION_ICON: "/icons/notification-icon.png";
    readonly DEFAULT_NOTIFICATION_BADGE: "/icons/notification-badge.png";
    readonly SERVICE_WORKER_PATH: "/firebase-messaging-sw.js";
    readonly SERVICE_WORKER_SCOPE: "/";
    readonly MAX_RETRY_ATTEMPTS: 3;
    readonly RETRY_DELAY_MS: 1000;
    readonly TOKEN_REFRESH_INTERVAL_MS: number;
};
/**
 * Predefined FCM topics for the application
 */
export declare const FCM_TOPICS: {
    readonly ADMIN_NOTIFICATIONS: "admin-notifications";
    readonly ADMIN_ALERTS: "admin-alerts";
    readonly ADMIN_REPORTS: "admin-reports";
    readonly USER_UPDATES: "user-updates";
    readonly NEWSLETTER: "newsletter";
    readonly CAMPAIGNS: "campaigns";
    readonly MARKETING: "marketing";
    readonly SYSTEM_ALERTS: "system-alerts";
    readonly MAINTENANCE: "maintenance";
    readonly GENERAL: "general";
    readonly ANNOUNCEMENTS: "announcements";
};
/**
 * Topic configurations with metadata
 */
export declare const TOPIC_CONFIGS: {
    readonly "admin-notifications": {
        readonly displayName: "Admin Notifications";
        readonly description: "Important notifications for administrators";
        readonly defaultSubscribed: false;
        readonly requiresPermission: true;
    };
    readonly "admin-alerts": {
        readonly displayName: "Admin Alerts";
        readonly description: "Critical alerts requiring immediate attention";
        readonly defaultSubscribed: false;
        readonly requiresPermission: true;
    };
    readonly "user-updates": {
        readonly displayName: "Account Updates";
        readonly description: "Updates about your account and profile";
        readonly defaultSubscribed: true;
        readonly requiresPermission: false;
    };
    readonly newsletter: {
        readonly displayName: "Newsletter";
        readonly description: "Weekly newsletter and updates";
        readonly defaultSubscribed: false;
        readonly requiresPermission: false;
    };
    readonly campaigns: {
        readonly displayName: "Marketing Campaigns";
        readonly description: "New marketing campaigns and promotions";
        readonly defaultSubscribed: false;
        readonly requiresPermission: false;
    };
    readonly marketing: {
        readonly displayName: "Marketing Updates";
        readonly description: "Marketing tips, insights, and best practices";
        readonly defaultSubscribed: false;
        readonly requiresPermission: false;
    };
    readonly "system-alerts": {
        readonly displayName: "System Alerts";
        readonly description: "System maintenance and service updates";
        readonly defaultSubscribed: true;
        readonly requiresPermission: false;
    };
    readonly general: {
        readonly displayName: "General Notifications";
        readonly description: "General updates and announcements";
        readonly defaultSubscribed: true;
        readonly requiresPermission: false;
    };
};
//# sourceMappingURL=index.d.ts.map