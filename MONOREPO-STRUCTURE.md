# 🏗️ Encreasl Monorepo - Complete Architecture Documentation

**Enterprise-Grade Turborepo Monorepo for Ecommerce Marketing Agency**

[![Turborepo](https://img.shields.io/badge/Turborepo-2.5.5-blue.svg)](https://turbo.build/)
[![pnpm](https://img.shields.io/badge/pnpm-10.12.1-orange.svg)](https://pnpm.io/)
[![Next.js](https://img.shields.io/badge/Next.js-15.4.2-black.svg)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.x-blue.svg)](https://www.typescriptlang.org/)
[![Firebase](https://img.shields.io/badge/Firebase-13.4.0-yellow.svg)](https://firebase.google.com/)

---

## 📋 Table of Contents

- [🎯 Overview](#-overview)
- [🏗️ Architecture](#️-architecture)
- [📁 Directory Structure](#-directory-structure)
- [📦 Applications](#-applications)
- [🔧 Shared Packages](#-shared-packages)
- [⚡ Firebase Functions](#-firebase-functions)
- [🛠️ Configuration Files](#️-configuration-files)
- [📚 Documentation](#-documentation)
- [🚀 Scripts & Automation](#-scripts--automation)
- [🌍 Environment Management](#-environment-management)
- [🔄 Development Workflow](#-development-workflow)
- [📊 Build & Deployment](#-build--deployment)

---

## 🎯 Overview

**Encreasl** is a production-ready, enterprise-grade monorepo built with modern web technologies and best practices. It provides a scalable foundation for ecommerce marketing agency operations with comprehensive admin management, user systems, and real-time notifications.

### **🏆 Key Features**

- ✅ **Enterprise Monorepo Architecture** - Turborepo + pnpm workspaces
- ✅ **Full-Stack Applications** - Next.js 15 with App Router
- ✅ **Firebase Backend** - 13+ Cloud Functions with complex business logic
- ✅ **Shared Package System** - Reusable components and utilities
- ✅ **Type-Safe Environment** - Zod validation with hierarchical configs
- ✅ **Professional Admin System** - Role-based access control
- ✅ **Real-Time Notifications** - Firebase Cloud Messaging integration
- ✅ **Production Deployment** - Vercel + Firebase optimized

---

## 🏗️ Architecture

```mermaid
graph TB
    subgraph "Client Layer"
        WEB[🌐 Web App<br/>Marketing Site]
        ADMIN[👨‍💼 Admin Dashboard<br/>Management Portal]
    end
    
    subgraph "Shared Packages"
        UI[🎨 @encreasl/ui<br/>Components]
        AUTH[🔐 @encreasl/auth<br/>Authentication]
        FCM[📱 @encreasl/fcm<br/>Messaging]
        ENV[🌍 @encreasl/env<br/>Environment]
        CONFIG[⚙️ Config Packages<br/>ESLint, TypeScript]
    end
    
    subgraph "Backend Services"
        FUNCTIONS[⚡ Firebase Functions<br/>13+ Cloud Functions]
        FIRESTORE[(🗄️ Firestore<br/>Database)]
        STORAGE[📁 Cloud Storage<br/>File Management]
        MESSAGING[📨 FCM<br/>Push Notifications]
    end
    
    WEB --> UI
    ADMIN --> UI
    WEB --> AUTH
    ADMIN --> AUTH
    WEB --> FCM
    ADMIN --> FCM
    
    AUTH --> FUNCTIONS
    FCM --> FUNCTIONS
    FUNCTIONS --> FIRESTORE
    FUNCTIONS --> STORAGE
    FUNCTIONS --> MESSAGING
```

---

## 📁 Directory Structure

```
encreasl/                                    # 🏠 Monorepo Root
├── 📁 apps/                                 # 🚀 Applications
│   ├── 📁 web/                              # 🌐 Marketing Website (Port 3000)
│   │   ├── 📁 src/
│   │   │   ├── 📁 app/                      # Next.js App Router
│   │   │   │   ├── 📁 api/                  # API Routes
│   │   │   │   │   ├── 📁 contact/          # Contact form endpoints
│   │   │   │   │   └── 📁 fcm/              # FCM token management
│   │   │   │   ├── 📄 layout.tsx            # Root layout
│   │   │   │   ├── 📄 page.tsx              # Homepage
│   │   │   │   └── 📄 globals.css           # Global styles
│   │   │   ├── 📁 components/               # React components
│   │   │   │   ├── 📄 ContactForm.tsx       # Contact form component
│   │   │   │   ├── 📄 Hero.tsx              # Hero section
│   │   │   │   └── 📄 Navigation.tsx        # Navigation component
│   │   │   └── 📁 lib/                      # Utilities
│   │   │       ├── 📄 env.ts                # Environment validation
│   │   │       └── 📄 utils.ts              # Helper functions
│   │   ├── 📄 next.config.ts                # Next.js configuration
│   │   ├── 📄 tailwind.config.ts            # Tailwind CSS config
│   │   ├── 📄 package.json                  # Dependencies
│   │   └── 📄 .env.example                  # Environment template
│   └── 📁 web-admin/                        # 👨‍💼 Admin Dashboard (Port 3001)
│       ├── 📁 src/
│       │   ├── 📁 app/
│       │   │   ├── 📁 api/                  # Admin API routes
│       │   │   │   ├── 📁 admin/            # Admin management
│       │   │   │   │   ├── 📁 fcm/          # Admin FCM endpoints
│       │   │   │   │   └── 📁 users/        # User management
│       │   │   │   ├── 📁 auth/             # Authentication
│       │   │   │   │   └── 📁 verify-admin/ # Admin verification
│       │   │   │   ├── 📁 roles/            # Role management
│       │   │   │   └── 📁 users/            # User operations
│       │   │   ├── 📁 admin/                # Admin pages
│       │   │   ├── 📁 login/                # Login page
│       │   │   ├── 📄 layout.tsx            # Admin layout
│       │   │   └── 📄 page.tsx              # Dashboard home
│       │   ├── 📁 components/               # Admin components
│       │   │   ├── 📄 AdminDashboard.tsx    # Main dashboard
│       │   │   └── 📁 auth/                 # Auth components
│       │   ├── 📁 contexts/                 # React contexts
│       │   │   └── 📄 AuthContext.tsx       # Authentication context
│       │   ├── 📁 hooks/                    # Custom hooks
│       │   │   └── 📄 useAuth.ts            # Authentication hook
│       │   └── 📁 lib/                      # Admin utilities
│       │       ├── 📄 env.ts                # Environment validation
│       │       └── 📄 firebase.ts           # Firebase configuration
│       ├── 📄 next.config.ts                # Next.js configuration
│       ├── 📄 package.json                  # Dependencies
│       └── 📄 .env.example                  # Environment template
├── 📁 packages/                             # 📦 Shared Packages
│   ├── 📁 ui/                               # 🎨 UI Components Library
│   │   ├── 📁 src/
│   │   │   ├── 📄 button.tsx                # Button component
│   │   │   ├── 📄 card.tsx                  # Card component
│   │   │   ├── 📄 input.tsx                 # Input component
│   │   │   └── 📄 index.ts                  # Exports
│   │   ├── 📄 package.json                  # Package configuration
│   │   └── 📄 tsconfig.json                 # TypeScript config
│   ├── 📁 auth/                             # 🔐 Authentication Package
│   │   ├── 📁 src/
│   │   │   ├── 📄 auth-service.ts           # Core auth service
│   │   │   ├── 📄 firebase-admin.ts         # Server-side auth
│   │   │   ├── 📄 hooks.ts                  # React hooks
│   │   │   ├── 📄 types.ts                  # Auth types
│   │   │   ├── 📄 utils.ts                  # Auth utilities
│   │   │   ├── 📄 index.ts                  # Client exports
│   │   │   └── 📁 server/                   # Server-only exports
│   │   │       └── 📄 index.ts              # Server exports
│   │   ├── 📄 server.ts                     # Server entry point
│   │   ├── 📄 package.json                  # Package configuration
│   │   └── 📄 tsconfig.json                 # TypeScript config
│   ├── 📁 fcm/                              # 📱 Firebase Cloud Messaging
│   │   ├── 📁 src/
│   │   │   ├── 📁 client/                   # Client-side utilities
│   │   │   │   ├── 📄 hooks.ts              # React hooks (useFCM)
│   │   │   │   ├── 📄 browser.ts            # Browser APIs
│   │   │   │   └── 📄 service-worker.ts     # SW integration
│   │   │   ├── 📁 server/                   # Server-side utilities
│   │   │   │   ├── 📄 admin.ts              # Admin SDK helpers
│   │   │   │   └── 📄 operations.ts         # Server operations
│   │   │   ├── 📁 types/                    # TypeScript definitions
│   │   │   │   ├── 📄 messages.ts           # Message types
│   │   │   │   ├── 📄 devices.ts            # Device types
│   │   │   │   └── 📄 topics.ts             # Topic types
│   │   │   ├── 📁 config/                   # Configuration
│   │   │   │   ├── 📄 index.ts              # Config exports
│   │   │   │   └── 📄 topics.ts             # Topic definitions
│   │   │   ├── 📁 utils/                    # Common utilities
│   │   │   │   ├── 📄 validation.ts         # Message validation
│   │   │   │   ├── 📄 formatting.ts         # Message formatting
│   │   │   │   └── 📄 errors.ts             # Error handling
│   │   │   └── 📄 index.ts                  # Main exports
│   │   ├── 📄 package.json                  # Package configuration
│   │   └── 📄 tsconfig.json                 # TypeScript config
│   ├── 📁 env/                              # 🌍 Environment Management
│   │   ├── 📁 src/
│   │   │   ├── 📄 shared.ts                 # Shared env variables
│   │   │   ├── 📄 web.ts                    # Web app env
│   │   │   ├── 📄 admin.ts                  # Admin app env
│   │   │   ├── 📄 validation.ts             # Zod schemas
│   │   │   └── 📄 index.ts                  # Exports
│   │   ├── 📄 package.json                  # Package configuration
│   │   └── 📄 tsconfig.json                 # TypeScript config
│   ├── 📁 eslint-config/                    # 🔧 ESLint Configuration
│   │   ├── 📄 base.js                       # Base ESLint config
│   │   ├── 📄 nextjs.js                     # Next.js specific config
│   │   └── 📄 package.json                  # Package configuration
│   └── 📁 typescript-config/               # 📘 TypeScript Configuration
│       ├── 📄 base.json                     # Base TypeScript config
│       ├── 📄 nextjs.json                   # Next.js specific config
│       └── 📄 package.json                  # Package configuration
├── 📁 functions/                            # ⚡ Firebase Cloud Functions
│   ├── 📁 src/
│   │   ├── 📁 admin/                        # 👨‍💼 Admin System
│   │   │   ├── 📄 adminAuth.ts              # Admin authentication
│   │   │   ├── 📄 adminFunctions.ts         # Admin CRUD operations
│   │   │   ├── 📄 adminTypes.ts             # Admin type definitions
│   │   │   ├── 📄 createAdminCollections.ts # Collection setup
│   │   │   ├── 📄 createJohnAdmin.ts        # Default admin creation
│   │   │   ├── 📄 roleManagement.ts         # Role & permission system
│   │   │   └── 📄 setupAdmin.ts             # Admin system initialization
│   │   ├── 📁 triggers/                     # 🎯 Event Triggers
│   │   │   ├── 📄 callable-functions.ts     # Client-callable functions
│   │   │   ├── 📄 campaign-notifications.ts # Campaign event handlers
│   │   │   ├── 📄 contact-notifications.ts  # Contact form handlers
│   │   │   ├── 📄 http-notifications.ts     # HTTP API endpoints
│   │   │   ├── 📄 newsletter-notifications.ts # Newsletter handlers
│   │   │   ├── 📄 pubsub-notifications.ts   # PubSub processors
│   │   │   ├── 📄 scheduled-notifications.ts # Cron jobs
│   │   │   ├── 📄 user-auth-triggers.ts     # Auth event handlers
│   │   │   └── 📄 user-notifications.ts     # User lifecycle handlers
│   │   ├── 📁 services/                     # 🔧 Business Logic Services
│   │   │   └── 📄 fcm-service.ts            # FCM service implementation
│   │   ├── 📁 users/                        # 👥 User Management
│   │   │   ├── 📄 userFunctions.ts          # User CRUD operations
│   │   │   └── 📄 userUtils.ts              # User utilities
│   │   ├── 📁 types/                        # 📝 Type Definitions
│   │   │   ├── 📄 notifications.ts          # Notification types
│   │   │   └── 📄 users.ts                  # User types
│   │   └── 📄 index.ts                      # Function exports
│   ├── 📁 lib/                              # 📦 Compiled JavaScript
│   ├── 📄 package.json                      # Dependencies
│   ├── 📄 tsconfig.json                     # TypeScript configuration
│   └── 📄 .eslintrc.js                      # ESLint configuration
├── 📁 docs/                                 # 📚 Documentation
│   ├── 📄 admin-schema.md                   # Admin system schema
│   ├── 📄 admin-system-setup.md             # Admin setup guide
│   ├── 📄 environment-variables.md          # Environment guide
│   ├── 📄 fcm-deployment-guide.md           # FCM deployment
│   ├── 📄 firebase-cloud-messaging.md       # FCM implementation
│   ├── 📄 firebase-principles.md            # Firebase best practices
│   └── 📄 users-schema.md                   # User system schema
├── 📁 scripts/                              # 🚀 Automation Scripts
│   ├── 📄 create-admin-user.js              # Admin user creation
│   ├── 📄 create-john-admin.js              # Default admin setup
│   ├── 📄 deploy-fcm.js                     # FCM deployment automation
│   ├── 📄 link-john-admin-uid.js            # Admin UID linking
│   ├── 📄 setup-env.js                      # Environment setup
│   ├── 📄 verify-john-admin.js              # Admin verification
│   ├── 📄 verify-setup.js                   # Setup verification
│   ├── 📄 package.json                      # Script dependencies
│   └── 📄 README.md                         # Scripts documentation
├── 📁 templates/                            # 📋 Code Templates
│   └── 📄 firebase-messaging-sw-web.js      # Service worker template
├── 📄 package.json                          # 🏠 Root package configuration
├── 📄 pnpm-workspace.yaml                   # 📦 pnpm workspace config
├── 📄 turbo.json                            # ⚡ Turborepo configuration
├── 📄 vercel.json                           # 🚀 Vercel deployment config
├── 📄 firebase.json                         # 🔥 Firebase project config
├── 📄 .env.example                          # 🌍 Environment template
├── 📄 README.md                             # 📖 Main documentation
├── 📄 README-FCM-COMPLETE.md                # 📱 FCM implementation guide
└── 📄 MONOREPO-STRUCTURE.md                 # 🏗️ This document
```

---

## 📦 Applications

### 🌐 Web App (`@encreasl/web`)
**Marketing Website - Port 3000**

- **Framework**: Next.js 15 with App Router
- **Styling**: Tailwind CSS 4.x
- **Purpose**: Public-facing marketing website
- **Features**:
  - ✅ Static site generation (SSG)
  - ✅ Contact form with Firebase integration
  - ✅ FCM token management
  - ✅ SEO optimized
  - ✅ Responsive design
  - ✅ Performance optimized

**Key Components:**
- `ContactForm.tsx` - Lead generation form
- `Hero.tsx` - Landing page hero section
- `Navigation.tsx` - Site navigation

### 👨‍💼 Admin Dashboard (`@encreasl/web-admin`)
**Management Portal - Port 3001**

- **Framework**: Next.js 15 with App Router
- **Authentication**: Firebase Auth with custom claims
- **Purpose**: Internal admin management system
- **Features**:
  - ✅ Role-based access control (RBAC)
  - ✅ User management interface
  - ✅ Real-time notifications
  - ✅ Admin analytics dashboard
  - ✅ FCM token management
  - ✅ Audit logging

**Key Components:**
- `AdminDashboard.tsx` - Main dashboard interface
- `AuthContext.tsx` - Authentication state management
- `useAuth.ts` - Authentication hook

---

## 🔧 Shared Packages

### 🎨 UI Package (`@encreasl/ui`)
**Reusable React Components**

```typescript
// Usage example
import { Button, Card, Input } from '@encreasl/ui';

export function MyComponent() {
  return (
    <Card>
      <Input placeholder="Enter text" />
      <Button variant="primary">Submit</Button>
    </Card>
  );
}
```

**Components:**
- `Button` - Customizable button component
- `Card` - Container component
- `Input` - Form input component
- `Modal` - Modal dialog component
- `Spinner` - Loading indicator

### 🔐 Auth Package (`@encreasl/auth`)
**Firebase Authentication Utilities**

```typescript
// Client-side usage
import { useAuth, AuthService } from '@encreasl/auth';

// Server-side usage
import { verifyIdToken, verifyAdminUser } from '@encreasl/auth/server';
```

**Features:**
- ✅ Email/password authentication
- ✅ Google OAuth integration
- ✅ Admin user verification
- ✅ Custom claims management
- ✅ React hooks for auth state
- ✅ Server-side token verification

### 📱 FCM Package (`@encreasl/fcm`)
**Firebase Cloud Messaging Integration**

```typescript
// Client-side usage
import { useFCM, FCMProvider } from '@encreasl/fcm/client';

// Server-side usage
import { sendToTopic, sendToTokens } from '@encreasl/fcm/server';
```

**Features:**
- ✅ React hooks for FCM integration
- ✅ Service worker management
- ✅ Topic subscription/unsubscription
- ✅ Token management
- ✅ Message formatting utilities
- ✅ Server-side sending capabilities

### 🌍 Environment Package (`@encreasl/env`)
**Type-Safe Environment Management**

```typescript
// Usage example
import { getWebEnv, getAdminEnv } from '@encreasl/env';

const webConfig = getWebEnv();
const adminConfig = getAdminEnv();
```

**Features:**
- ✅ Zod schema validation
- ✅ Hierarchical environment variables
- ✅ Type-safe configuration
- ✅ App-specific environments
- ✅ Development/production modes

### ⚙️ Configuration Packages

**ESLint Config (`@encreasl/eslint-config`)**
- Base ESLint configuration
- Next.js specific rules
- TypeScript integration
- Import/export rules

**TypeScript Config (`@encreasl/typescript-config`)**
- Base TypeScript configuration
- Next.js specific settings
- Strict type checking
- Path mapping support

---

## ⚡ Firebase Functions

### 📊 Function Categories

**🎯 Trigger Functions (Event-Driven)**
- `onContactSubmitted` - Contact form processing
- `onNewsletterSubscribed` - Newsletter signup handling
- `onUserCreated` - User lifecycle management
- `onUserDeleted` - User cleanup operations
- `onUserUpdated` - User change notifications
- `onCampaignCreated` - Campaign launch notifications
- `onCampaignStatusChanged` - Campaign status updates

**📞 Callable Functions (Client-Invoked)**
- `subscribeToTopic` - FCM topic subscription
- `unsubscribeFromTopic` - FCM topic unsubscription
- `getUserNotificationPreferences` - Get user preferences
- `updateNotificationPreferences` - Update user preferences
- `createUser` - User creation
- `updateUser` - User updates
- `getUser` - User retrieval

**🌐 HTTP Functions (REST API)**
- `sendNotification` - Custom notification sending
- `manageFCMTokens` - Token management API
- `getNotificationAnalytics` - Analytics retrieval

**📅 Scheduled Functions (Cron Jobs)**
- `sendDailyDigest` - Daily summary emails
- `sendWeeklyReport` - Weekly analytics reports
- `cleanupOldNotifications` - Data cleanup

**🔄 PubSub Functions (Async Processing)**
- `processBulkNotifications` - Bulk notification processing
- `processNotificationRetries` - Failed notification retries
- `processNotificationAnalytics` - Analytics data processing

**👨‍💼 Admin Functions**
- `createAdminUser` - Admin user creation
- `updateAdminUser` - Admin user updates
- `createAdminRole` - Role creation
- `updateAdminRole` - Role updates
- `deleteAdminRole` - Role deletion
- `adminLogin` - Admin authentication
- `validateAdminSession` - Session validation
- `initializeAdminSystem` - System setup

### 🔧 Services

**FCM Service (`fcm-service.ts`)**
- Message sending to tokens/topics/conditions
- Device management
- Analytics tracking
- Retry logic
- Priority handling

---

## 🛠️ Configuration Files

### ⚡ Turborepo Configuration (`turbo.json`)

```json
{
  "ui": "tui",
  "globalDependencies": [".env.local", ".env"],
  "globalEnv": ["NODE_ENV", "VERCEL_ENV", "CI"],
  "globalPassThroughEnv": ["NEXT_PUBLIC_*", "FIREBASE_*", "ADMIN_*"],
  "tasks": {
    "build": {
      "dependsOn": ["^build"],
      "outputs": [".next/**", "dist/**", "lib/**"],
      "env": ["NEXT_PUBLIC_*", "FIREBASE_*", "ADMIN_*"]
    },
    "dev": {
      "cache": false,
      "persistent": true
    }
  }
}
```

### 📦 Workspace Configuration (`pnpm-workspace.yaml`)

```yaml
packages:
  - "apps/*"
  - "packages/*"
  - "functions"
```

### 🚀 Deployment Configuration (`vercel.json`)

```json
{
  "buildCommand": "turbo run build",
  "installCommand": "pnpm install",
  "framework": "nextjs"
}
```

### 🔥 Firebase Configuration (`firebase.json`)

```json
{
  "functions": {
    "source": "functions",
    "runtime": "nodejs18"
  },
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "hosting": {
    "public": "apps/web/out",
    "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]
  }
}
```

---

## 📚 Documentation

### 📖 Available Documentation

- **`README.md`** - Main project documentation
- **`README-FCM-COMPLETE.md`** - Complete FCM implementation guide
- **`docs/environment-variables.md`** - Environment variable strategy
- **`docs/firebase-cloud-messaging.md`** - FCM implementation details
- **`docs/firebase-principles.md`** - Firebase best practices
- **`docs/admin-schema.md`** - Admin system database schema
- **`docs/admin-system-setup.md`** - Admin system setup guide
- **`docs/users-schema.md`** - User system database schema
- **`docs/fcm-deployment-guide.md`** - FCM deployment instructions

### 📋 Schema Documentation

**Admin System Collections:**
- `/admin_users/{adminId}` - Admin user documents
- `/admin_roles/{roleId}` - Role definitions
- `/admin_permissions/{permissionId}` - Permission definitions
- `/admin_sessions/{sessionId}` - Active admin sessions
- `/admin_audit_logs/{logId}` - Admin activity logs

**User System Collections:**
- `/users/{userId}` - User profile documents
- `/user_preferences/{userId}` - User preferences
- `/notification_preferences/{userId}` - Notification settings

**Notification System Collections:**
- `/notification_logs/{logId}` - Notification history
- `/notification_analytics/{analyticsId}` - Analytics data
- `/fcm_tokens/{tokenId}` - FCM device tokens

---

## 🚀 Scripts & Automation

### 🔧 Available Scripts

**Root Level Scripts:**
```bash
# Development
pnpm dev                    # Start all development servers
pnpm dev:web               # Start web app only
pnpm dev:admin             # Start admin app only
pnpm dev:functions         # Start functions emulator

# Building
pnpm build                 # Build all packages
pnpm build:web             # Build web app only
pnpm build:admin           # Build admin app only
pnpm build:functions       # Build functions only

# Quality Assurance
pnpm lint                  # Lint all packages
pnpm lint:functions        # Lint functions only
pnpm type-check            # Type check all packages
pnpm type-check:functions  # Type check functions only

# Testing & Deployment
pnpm test:functions        # Run function tests
pnpm deploy:functions      # Deploy functions
pnpm serve:functions       # Serve functions locally

# Utilities
pnpm setup                 # Environment setup
pnpm clean                 # Clean build artifacts
```

### 🛠️ Automation Scripts (`scripts/`)

- **`setup-env.js`** - Environment variable setup automation
- **`create-admin-user.js`** - Admin user creation script
- **`create-john-admin.js`** - Default admin setup
- **`deploy-fcm.js`** - Complete FCM deployment automation
- **`verify-setup.js`** - Setup verification script
- **`verify-john-admin.js`** - Admin verification script
- **`link-john-admin-uid.js`** - Admin UID linking script

---

## 🌍 Environment Management

### 📁 Environment File Structure

```
encreasl/
├── .env.example                    # Root environment template
├── .env.local                      # Root environment variables
├── apps/web/.env.example           # Web app template
├── apps/web/.env.local             # Web app variables
├── apps/web-admin/.env.example     # Admin app template
└── apps/web-admin/.env.local       # Admin app variables
```

### 🔒 Environment Variable Categories

**🌍 Shared Variables (Root `.env.local`)**
- Firebase project configuration
- Shared API keys
- Database connections
- Third-party service keys

**🌐 Web App Variables (`apps/web/.env.local`)**
- Public Firebase config
- Marketing tool integrations
- Analytics tracking IDs
- Contact form settings

**👨‍💼 Admin App Variables (`apps/web-admin/.env.local`)**
- Admin Firebase config
- Admin-specific API keys
- Database access credentials
- Monitoring service keys

### ✅ Environment Validation

All environment variables are validated using **Zod schemas** with:
- Type safety enforcement
- Required field validation
- Format validation (URLs, emails, etc.)
- Development/production mode handling

---

## 🔄 Development Workflow

### 🚀 Getting Started

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd encreasl
   pnpm install
   ```

2. **Environment Setup**
   ```bash
   pnpm setup
   # Follow prompts to configure environment variables
   ```

3. **Start Development**
   ```bash
   pnpm dev
   # Web app: http://localhost:3000
   # Admin app: http://localhost:3001
   ```

### 🔧 Development Commands

```bash
# Start specific apps
pnpm dev:web               # Web app only
pnpm dev:admin             # Admin app only
pnpm dev:functions         # Functions emulator

# Build and test
pnpm build                 # Build all packages
pnpm lint                  # Lint all code
pnpm type-check            # Type checking

# Package management
pnpm add <package>         # Add to root
pnpm add <package> --filter=@encreasl/web  # Add to specific app
```

### 📦 Adding New Packages

```bash
# Create new shared package
mkdir packages/new-package
cd packages/new-package
pnpm init

# Add to workspace
# Edit pnpm-workspace.yaml if needed

# Install in apps
pnpm add @encreasl/new-package --filter=@encreasl/web
```

---

## 📊 Build & Deployment

### 🏗️ Build Process

**Turborepo Build Pipeline:**
1. **Dependency Resolution** - pnpm installs all dependencies
2. **Package Building** - Shared packages built first
3. **App Building** - Applications built with package dependencies
4. **Function Building** - Firebase Functions compiled
5. **Optimization** - Code splitting, tree shaking, minification

### 🚀 Deployment Targets

**🌐 Web App Deployment (Vercel)**
```bash
# Automatic deployment on git push
git push origin main

# Manual deployment
vercel --prod
```

**👨‍💼 Admin App Deployment (Vercel)**
```bash
# Deploy admin app
vercel --prod --cwd apps/web-admin
```

**⚡ Functions Deployment (Firebase)**
```bash
# Deploy all functions
pnpm deploy:functions

# Deploy specific function
firebase deploy --only functions:onContactSubmitted
```

### 📈 Performance Optimizations

- **Turborepo Caching** - Intelligent build caching
- **Code Splitting** - Automatic route-based splitting
- **Tree Shaking** - Dead code elimination
- **Image Optimization** - Next.js automatic image optimization
- **Bundle Analysis** - Built-in bundle analyzer

---

## 🎯 Key Benefits

### 🏢 Enterprise-Grade Architecture
- ✅ **Scalable Monorepo** - Handles multiple teams and projects
- ✅ **Type Safety** - End-to-end TypeScript coverage
- ✅ **Code Reusability** - Shared packages across applications
- ✅ **Consistent Tooling** - Unified linting, formatting, and building

### 🚀 Developer Experience
- ✅ **Fast Development** - Turborepo + pnpm for speed
- ✅ **Hot Reloading** - Instant feedback during development
- ✅ **Intelligent Caching** - Build only what changed
- ✅ **Comprehensive Tooling** - ESLint, TypeScript, Prettier

### 🔒 Production Ready
- ✅ **Security Best Practices** - Environment variable validation
- ✅ **Error Handling** - Comprehensive error boundaries
- ✅ **Monitoring** - Built-in logging and analytics
- ✅ **Performance** - Optimized builds and deployments

---

## 📞 Support & Maintenance

### 🔧 Troubleshooting

**Common Issues:**
- Environment variable validation errors
- Package dependency conflicts
- Build cache issues
- Firebase deployment problems

**Solutions:**
- Check environment variable documentation
- Clear node_modules and reinstall
- Clear Turborepo cache: `turbo prune`
- Verify Firebase project configuration

### 📚 Additional Resources

- [Turborepo Documentation](https://turbo.build/repo/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [Firebase Documentation](https://firebase.google.com/docs)
- [pnpm Documentation](https://pnpm.io/motivation)

---

**🏆 This monorepo represents enterprise-grade architecture with modern best practices, providing a solid foundation for scalable web applications and complex business logic.**
