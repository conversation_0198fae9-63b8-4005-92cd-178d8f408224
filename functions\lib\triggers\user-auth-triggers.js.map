{"version": 3, "file": "user-auth-triggers.js", "sourceRoot": "", "sources": ["../../src/triggers/user-auth-triggers.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;;;AAEH,+DAAuF;AACvF,8CAA+C;AAC/C,wDAAmE;AACnE,8CAA8C;AAC9C,kDAAwE;AAGxE,MAAM,EAAE,GAAG,IAAA,wBAAY,GAAE,CAAC;AAC1B,MAAM,IAAI,GAAG,IAAA,cAAO,GAAE,CAAC;AAEvB,2CAA2C;AAC3C,qCAAqC;AACrC,2CAA2C;AAE3C;;;GAGG;AACU,QAAA,iBAAiB,GAAG,IAAA,6BAAiB,EAChD;IACE,QAAQ,EAAE,gBAAgB;IAC1B,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,EAAE;CACnB,EACD,KAAK,EAAE,KAAK,EAAE,EAAE;;IACd,IAAI,CAAC;QACH,MAAM,QAAQ,GAAG,MAAA,KAAK,CAAC,IAAI,0CAAE,IAAI,EAAE,CAAC;QACpC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;QAEnC,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,WAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;YAC9C,OAAO;QACT,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,kCAAkC,MAAM,EAAE,CAAC,CAAC;QAExD,iEAAiE;QACjE,MAAM,YAAY,GAAG,MAAM,IAAA,uBAAW,EAAC,MAAM,CAAC,CAAC;QAC/C,IAAI,YAAY,IAAI,YAAY,CAAC,OAAO,GAAG,CAAC,EAAE,CAAC;YAC7C,WAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,gCAAgC,CAAC,CAAC;YAC5D,OAAO;QACT,CAAC;QAED,kDAAkD;QAClD,IAAI,QAAQ,CAAC;QACb,IAAI,CAAC;YACH,QAAQ,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QACxC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,WAAM,CAAC,IAAI,CAAC,2BAA2B,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;QAC3D,CAAC;QAED,+CAA+C;QAC/C,MAAM,OAAO,GAAkB,EAAE,CAAC;QAClC,IAAI,WAAW,GAAG,KAAK,CAAC;QAExB,wCAAwC;QACxC,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAC;YACxB,OAAO,CAAC,SAAS,GAAG;gBAClB,YAAY,EAAE,KAAK;gBACnB,aAAa,EAAE,CAAC;gBAChB,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,qBAAS,CAAC,GAAG,EAAE;aAC9B,CAAC;YACF,WAAW,GAAG,IAAI,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;YACzB,OAAO,CAAC,UAAU,GAAG;gBACnB,aAAa,EAAE,IAAI;gBACnB,aAAa,EAAE,IAAI;aACpB,CAAC;YACF,WAAW,GAAG,IAAI,CAAC;QACrB,CAAC;QAED,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC;YAC1B,OAAO,CAAC,WAAW,GAAG;gBACpB,KAAK,EAAE,OAAO;gBACd,QAAQ,EAAE,IAAI;gBACd,QAAQ,EAAE,KAAK;gBACf,UAAU,EAAE,YAAY;gBACxB,UAAU,EAAE,KAAK;gBACjB,QAAQ,EAAE,KAAK;gBACf,aAAa,EAAE;oBACb,KAAK,EAAE,IAAI;oBACX,IAAI,EAAE,IAAI;oBACV,GAAG,EAAE,KAAK;oBACV,SAAS,EAAE,KAAK;iBACjB;gBACD,OAAO,EAAE;oBACP,iBAAiB,EAAE,QAAQ;oBAC3B,SAAS,EAAE,KAAK;oBAChB,SAAS,EAAE,KAAK;oBAChB,kBAAkB,EAAE,IAAI;iBACzB;aACF,CAAC;YACF,WAAW,GAAG,IAAI,CAAC;QACrB,CAAC;QAED,4CAA4C;QAC5C,IAAI,QAAQ,EAAE,CAAC;YACb,IAAI,QAAQ,CAAC,aAAa,KAAK,QAAQ,CAAC,aAAa,EAAE,CAAC;gBACtD,OAAO,CAAC,aAAa,GAAG,QAAQ,CAAC,aAAa,CAAC;gBAC/C,WAAW,GAAG,IAAI,CAAC;YACrB,CAAC;YAED,IAAI,QAAQ,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAC5C,OAAO,CAAC,QAAQ,GAAG,QAAQ,CAAC,QAAQ,CAAC;gBACrC,WAAW,GAAG,IAAI,CAAC;YACrB,CAAC;YAED,0BAA0B;YAC1B,IAAI,QAAQ,CAAC,YAAY,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACrC,MAAM,QAAQ,GAAG,QAAQ,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC;gBACrD,IAAI,YAAY,GAA8C,OAAO,CAAC;gBAEtE,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;oBAAE,YAAY,GAAG,QAAQ,CAAC;qBACpD,IAAI,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC;oBAAE,YAAY,GAAG,UAAU,CAAC;qBAC7D,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC;oBAAE,YAAY,GAAG,OAAO,CAAC;gBAE5D,IAAI,QAAQ,CAAC,YAAY,KAAK,YAAY,EAAE,CAAC;oBAC3C,OAAO,CAAC,YAAY,GAAG,YAAY,CAAC;oBACpC,WAAW,GAAG,IAAI,CAAC;gBACrB,CAAC;YACH,CAAC;QACH,CAAC;QAED,iCAAiC;QACjC,IAAI,WAAW,EAAE,CAAC;YAChB,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,iCAC1C,OAAO,KACV,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE,EAC1B,OAAO,EAAE,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,IACpC,CAAC;YACH,WAAM,CAAC,IAAI,CAAC,6BAA6B,MAAM,EAAE,CAAC,CAAC;QACrD,CAAC;QAED,uDAAuD;QACvD,MAAM,cAAc,GAAG,MAAM,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC;QACjF,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC;YAC3B,MAAM,IAAA,iCAAqB,EAAC,MAAM,CAAC,CAAC;YACpC,WAAM,CAAC,IAAI,CAAC,oCAAoC,MAAM,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,gDAAgD,MAAM,EAAE,CAAC,CAAC;IAExE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,WAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC5D,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CACF,CAAC;AAEF,2CAA2C;AAC3C,gCAAgC;AAChC,2CAA2C;AAE3C;;;GAGG;AACU,QAAA,aAAa,GAAG,IAAA,6BAAiB,EAC5C;IACE,QAAQ,EAAE,gBAAgB;IAC1B,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,EAAE;CACnB,EACD,KAAK,EAAE,KAAK,EAAE,EAAE;;IACd,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,MAAA,KAAK,CAAC,IAAI,0CAAE,MAAM,CAAC,IAAI,EAAE,CAAC;QAC7C,MAAM,SAAS,GAAG,MAAA,KAAK,CAAC,IAAI,0CAAE,KAAK,CAAC,IAAI,EAAE,CAAC;QAC3C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;QAEnC,IAAI,CAAC,UAAU,IAAI,CAAC,SAAS,EAAE,CAAC;YAC9B,WAAM,CAAC,KAAK,CAAC,qCAAqC,CAAC,CAAC;YACpD,OAAO;QACT,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,2BAA2B,MAAM,EAAE,CAAC,CAAC;QAEjD,uBAAuB;QACvB,IAAI,UAAU,CAAC,WAAW,KAAK,SAAS,CAAC,WAAW,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;YAC9E,mBAAmB;YACnB,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;gBAC9C,yBAAyB,EAAE,CAAC,CAAA,MAAA,SAAS,CAAC,SAAS,0CAAE,aAAa,KAAI,CAAC,CAAC,GAAG,CAAC;gBACxE,uBAAuB,EAAE,SAAS,CAAC,WAAW;aAC/C,CAAC,CAAC;QACL,CAAC;QAED,0DAA0D;QAC1D,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,WAAW,CAAC,EAAE,CAAC;YACrF,MAAM,iBAAiB,mCAClB,SAAS,CAAC,WAAW,KACxB,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE,GAC3B,CAAC;YAEF,MAAM,EAAE,CAAC,UAAU,CAAC,kBAAkB,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,iBAAiB,CAAC,CAAC;YAC9E,WAAM,CAAC,IAAI,CAAC,+BAA+B,MAAM,EAAE,CAAC,CAAC;QACvD,CAAC;QAED,gCAAgC;QAChC,IAAI,UAAU,CAAC,QAAQ,KAAK,SAAS,CAAC,QAAQ,EAAE,CAAC;YAC/C,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;gBACxB,wDAAwD;gBACxD,WAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,cAAc,CAAC,CAAC;gBAC1C,qCAAqC;YACvC,CAAC;iBAAM,CAAC;gBACN,mBAAmB;gBACnB,WAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,cAAc,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC,UAAU,CAAC,SAAS,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;YACjD,WAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,eAAe,CAAC,CAAC;YAC3C,uDAAuD;QACzD,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,yCAAyC,MAAM,EAAE,CAAC,CAAC;IAEjE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,WAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACrD,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CACF,CAAC;AAEF,2CAA2C;AAC3C,mCAAmC;AACnC,2CAA2C;AAE3C;;;GAGG;AACU,QAAA,wBAAwB,GAAG,IAAA,6BAAiB,EACvD;IACE,QAAQ,EAAE,2BAA2B;IACrC,MAAM,EAAE,aAAa;IACrB,MAAM,EAAE,QAAQ;IAChB,cAAc,EAAE,EAAE;CACnB,EACD,KAAK,EAAE,KAAK,EAAE,EAAE;;IACd,IAAI,CAAC;QACH,MAAM,SAAS,GAAG,MAAA,KAAK,CAAC,IAAI,0CAAE,KAAK,CAAC,IAAI,EAAE,CAAC;QAC3C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;QAEnC,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,WAAM,CAAC,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAC3D,OAAO;QACT,CAAC;QAED,WAAM,CAAC,IAAI,CAAC,kCAAkC,MAAM,EAAE,CAAC,CAAC;QAExD,yDAAyD;QACzD,MAAM,eAAe,GAAG;YACtB,KAAK,EAAE,SAAS,CAAC,KAAK;YACtB,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,UAAU,EAAE,SAAS,CAAC,UAAU;YAChC,UAAU,EAAE,SAAS,CAAC,UAAU;YAChC,QAAQ,EAAE,SAAS,CAAC,QAAQ;YAC5B,aAAa,EAAE,SAAS,CAAC,aAAa;YACtC,OAAO,EAAE,SAAS,CAAC,OAAO;SAC3B,CAAC;QAEF,MAAM,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC;YAC9C,WAAW,EAAE,eAAe;YAC5B,SAAS,EAAE,qBAAS,CAAC,GAAG,EAAE;SAC3B,CAAC,CAAC;QAEH,WAAM,CAAC,IAAI,CAAC,iDAAiD,MAAM,EAAE,CAAC,CAAC;IAEzE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,WAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,KAAK,CAAC,CAAC;QAC5D,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC,CACF,CAAC"}