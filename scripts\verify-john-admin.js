/**
 * Verify John Admin User Script
 * 
 * This script verifies that the John admin user is properly set up
 * for login in the web-admin application.
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables from the root .env.local file
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Admin user details
const ADMIN_EMAIL = '<EMAIL>';

// Firebase Admin configuration
const serviceAccount = {
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
};

async function verifyJohnAdmin() {
  try {
    console.log('🔍 Verifying John Admin User Setup...');
    console.log('📧 Email:', ADMIN_EMAIL);
    console.log('📊 Project ID:', serviceAccount.projectId);

    // Initialize Firebase Admin
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.projectId,
      });
    }

    const db = admin.firestore();
    const auth = admin.auth();

    console.log('\n📋 VERIFICATION CHECKLIST\n');

    // 1. Check Firestore user document
    console.log('1️⃣  Checking Firestore user document...');
    try {
      const userQuery = await db.collection('users')
        .where('email', '==', ADMIN_EMAIL)
        .limit(1)
        .get();
      
      if (userQuery.empty) {
        console.log('   ❌ User document not found in Firestore users collection');
      } else {
        const userDoc = userQuery.docs[0];
        const userData = userDoc.data();
        console.log('   ✅ User document found in Firestore');
        console.log(`   🆔 Document ID: ${userDoc.id}`);
        console.log(`   👤 Display Name: ${userData.displayName}`);
        console.log(`   👑 Role: ${userData.role || 'Not set'}`);
        console.log(`   🔐 Permissions: ${userData.permissions?.join(', ') || 'None'}`);
        console.log(`   ✅ Active: ${userData.isActive ? 'Yes' : 'No'}`);
        console.log(`   📅 Created: ${userData.createdAt?.toDate?.()?.toLocaleDateString() || 'Unknown'}`);
      }
    } catch (error) {
      console.log('   ❌ Error checking Firestore user:', error.message);
    }

    // 2. Check admin role
    console.log('\n2️⃣  Checking admin role...');
    try {
      const adminRoleDoc = await db.collection('roles').doc('admin').get();
      if (adminRoleDoc.exists) {
        const roleData = adminRoleDoc.data();
        console.log('   ✅ Admin role exists');
        console.log(`   👑 Role name: ${roleData.name}`);
        console.log(`   📊 Level: ${roleData.level}`);
        console.log(`   👥 User count: ${roleData.userCount}`);
        console.log(`   🔐 Permissions: ${roleData.permissions?.join(', ') || 'None'}`);
      } else {
        console.log('   ❌ Admin role not found');
      }
    } catch (error) {
      console.log('   ❌ Error checking admin role:', error.message);
    }

    // 3. Try to check Firebase Auth (may fail due to permissions)
    console.log('\n3️⃣  Checking Firebase Auth user...');
    try {
      const authUser = await auth.getUserByEmail(ADMIN_EMAIL);
      console.log('   ✅ User exists in Firebase Auth');
      console.log(`   🆔 UID: ${authUser.uid}`);
      console.log(`   📧 Email verified: ${authUser.emailVerified ? 'Yes' : 'No'}`);
      console.log(`   👤 Display name: ${authUser.displayName || 'Not set'}`);
      console.log(`   📅 Created: ${new Date(authUser.metadata.creationTime).toLocaleDateString()}`);
      console.log(`   🔑 Last sign in: ${authUser.metadata.lastSignInTime ? new Date(authUser.metadata.lastSignInTime).toLocaleDateString() : 'Never'}`);
      
      // Check custom claims
      try {
        const customClaims = authUser.customClaims || {};
        console.log(`   🏷️  Custom claims: ${Object.keys(customClaims).length > 0 ? JSON.stringify(customClaims) : 'None'}`);
      } catch (claimsError) {
        console.log('   ⚠️  Could not check custom claims');
      }
    } catch (authError) {
      if (authError.code === 'auth/user-not-found') {
        console.log('   ❌ User NOT found in Firebase Auth');
        console.log('   📝 You need to create the user manually in Firebase Console:');
        console.log('   🔗 https://console.firebase.google.com/project/encreasl-daa43/authentication/users');
        console.log(`   📧 Email: ${ADMIN_EMAIL}`);
        console.log('   🔒 Password: @Iamachessgrandmaster23');
      } else {
        console.log('   ⚠️  Could not check Firebase Auth (permission issue)');
        console.log('   📝 Please verify manually in Firebase Console:');
        console.log('   🔗 https://console.firebase.google.com/project/encreasl-daa43/authentication/users');
      }
    }

    // 4. Summary and next steps
    console.log('\n📊 SUMMARY\n');
    console.log('✅ Firestore user document: Created');
    console.log('✅ Admin role: Configured');
    console.log('⚠️  Firebase Auth user: Needs manual verification');
    
    console.log('\n🎯 TO COMPLETE SETUP:\n');
    console.log('1. 🔗 Go to Firebase Console: https://console.firebase.google.com/project/encreasl-daa43/authentication/users');
    console.log('2. 📧 <NAME_EMAIL> exists in the users list');
    console.log('3. ➕ If not, click "Add user" and create:');
    console.log('   📧 Email: <EMAIL>');
    console.log('   🔒 Password: @Iamachessgrandmaster23');
    console.log('4. 🚀 Test login at your admin app');
    
    console.log('\n🔗 USEFUL LINKS:\n');
    console.log('📊 Firestore Console: https://console.firebase.google.com/project/encreasl-daa43/firestore/data');
    console.log('🔐 Authentication Console: https://console.firebase.google.com/project/encreasl-daa43/authentication/users');
    console.log('⚙️  Project Settings: https://console.firebase.google.com/project/encreasl-daa43/settings/general');

    console.log('\n✅ Verification completed!');

  } catch (error) {
    console.error('\n❌ Error during verification:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  verifyJohnAdmin()
    .then(() => {
      console.log('\n🏁 Verification script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Verification script failed:', error);
      process.exit(1);
    });
}

module.exports = { verifyJohnAdmin };
