/**
 * Newsletter Notification Triggers
 *
 * Cloud Functions that send notifications for newsletter events.
 */
export declare const onNewsletterSubscribed: import("firebase-functions/v2").CloudFunction<import("firebase-functions/v2/firestore").FirestoreEvent<import("firebase-functions/v2/firestore").QueryDocumentSnapshot | undefined, {
    subscriptionId: string;
}>>;
//# sourceMappingURL=newsletter-notifications.d.ts.map