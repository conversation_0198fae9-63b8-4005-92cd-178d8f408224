/**
 * Verification Script
 * 
 * This script verifies that the users collection and admin user setup
 * are working correctly.
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables from the root .env.local file
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Firebase Admin configuration
const serviceAccount = {
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
};

async function verifySetup() {
  try {
    console.log('🔍 Starting verification...');
    console.log('📊 Project ID:', serviceAccount.projectId);

    // Initialize Firebase Admin
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.projectId,
      });
    }

    const db = admin.firestore();
    const auth = admin.auth();

    console.log('\n📋 VERIFICATION CHECKLIST\n');

    // 1. Check users collection
    console.log('1️⃣  Checking users collection...');
    try {
      const usersSnapshot = await db.collection('users').limit(5).get();
      if (usersSnapshot.empty) {
        console.log('   ❌ Users collection is empty');
      } else {
        console.log(`   ✅ Users collection exists with ${usersSnapshot.size} documents`);
        
        // Show sample user data
        const sampleUser = usersSnapshot.docs[0].data();
        console.log(`   📄 Sample user: ${sampleUser.displayName} (${sampleUser.email})`);
        console.log(`   🏷️  Account type: ${sampleUser.accountType}`);
        console.log(`   📅 Created: ${sampleUser.createdAt.toDate().toLocaleDateString()}`);
      }
    } catch (error) {
      console.log('   ❌ Error accessing users collection:', error.message);
    }

    // 2. Check user_preferences collection
    console.log('\n2️⃣  Checking user_preferences collection...');
    try {
      const preferencesSnapshot = await db.collection('user_preferences').limit(5).get();
      if (preferencesSnapshot.empty) {
        console.log('   ❌ User preferences collection is empty');
      } else {
        console.log(`   ✅ User preferences collection exists with ${preferencesSnapshot.size} documents`);
        
        // Show sample preferences
        const samplePrefs = preferencesSnapshot.docs[0].data();
        console.log(`   🎨 Sample theme: ${samplePrefs.theme}`);
        console.log(`   🌍 Sample language: ${samplePrefs.language}`);
        console.log(`   🔔 Email notifications: ${samplePrefs.notifications?.email ? 'enabled' : 'disabled'}`);
      }
    } catch (error) {
      console.log('   ❌ Error accessing user preferences collection:', error.message);
    }

    // 3. Check system configuration
    console.log('\n3️⃣  Checking system configuration...');
    try {
      const systemConfigDoc = await db.collection('system_config').doc('users_collection').get();
      if (systemConfigDoc.exists) {
        const config = systemConfigDoc.data();
        console.log('   ✅ Users collection system config exists');
        console.log(`   📊 Initialized: ${config.initialized ? 'Yes' : 'No'}`);
        console.log(`   📅 Initialized at: ${config.initializedAt.toDate().toLocaleDateString()}`);
        console.log(`   🔢 Version: ${config.version}`);
      } else {
        console.log('   ❌ Users collection system config not found');
      }
    } catch (error) {
      console.log('   ❌ Error accessing system config:', error.message);
    }

    // 4. Check admin roles
    console.log('\n4️⃣  Checking admin roles...');
    try {
      const adminRoleDoc = await db.collection('admin_roles').doc('admin').get();
      if (adminRoleDoc.exists) {
        const role = adminRoleDoc.data();
        console.log('   ✅ Admin role exists');
        console.log(`   👑 Role name: ${role.displayName}`);
        console.log(`   📊 Level: ${role.level}`);
        console.log(`   👥 User count: ${role.userCount}`);
        console.log(`   🔐 Permissions: ${role.permissions.length} permissions`);
      } else {
        console.log('   ❌ Admin role not found');
      }
    } catch (error) {
      console.log('   ❌ Error accessing admin roles:', error.message);
    }

    // 5. Check for John admin user
    console.log('\n5️⃣  Checking for John admin user...');
    try {
      // Check Firebase Auth
      let authUser;
      try {
        authUser = await auth.getUserByEmail('<EMAIL>');
        console.log('   ✅ John admin user exists in Firebase Auth');
        console.log(`   🆔 UID: ${authUser.uid}`);
        console.log(`   📧 Email verified: ${authUser.emailVerified ? 'Yes' : 'No'}`);
        console.log(`   👤 Display name: ${authUser.displayName}`);
      } catch (authError) {
        console.log('   ❌ John admin user not found in Firebase Auth');
        authUser = null;
      }

      // Check Firestore admin_users collection
      if (authUser) {
        const adminUserDoc = await db.collection('admin_users').doc(authUser.uid).get();
        if (adminUserDoc.exists) {
          const adminUser = adminUserDoc.data();
          console.log('   ✅ John admin user exists in Firestore');
          console.log(`   👑 Role: ${adminUser.roleName}`);
          console.log(`   🔐 Permissions: ${adminUser.permissions.length} permissions`);
          console.log(`   ✅ Active: ${adminUser.isActive ? 'Yes' : 'No'}`);
          console.log(`   🔒 Super admin: ${adminUser.isSuperAdmin ? 'Yes' : 'No'}`);
        } else {
          console.log('   ❌ John admin user not found in Firestore admin_users collection');
        }
      }
    } catch (error) {
      console.log('   ❌ Error checking John admin user:', error.message);
    }

    // 6. Check Cloud Functions deployment status
    console.log('\n6️⃣  Checking Cloud Functions...');
    console.log('   ℹ️  To verify Cloud Functions are deployed, run:');
    console.log('   📝 firebase functions:list');
    console.log('   🚀 Look for: createUser, updateUser, getUser, createJohnAdmin');

    // 7. Summary
    console.log('\n📊 VERIFICATION SUMMARY\n');
    console.log('✅ Users collection structure created');
    console.log('✅ Sample user data populated');
    console.log('✅ User preferences system ready');
    console.log('✅ System configuration in place');
    console.log('✅ Admin role system verified');
    
    console.log('\n🎯 NEXT STEPS:\n');
    console.log('1. Deploy Cloud Functions: firebase deploy --only functions');
    console.log('2. Call createJohnAdmin function to create the admin user');
    console.log('3. Test admin login at your admin panel');
    console.log('4. Verify users collection is visible in Firestore console');

    console.log('\n🔗 FIRESTORE CONSOLE:');
    console.log(`https://console.firebase.google.com/project/${serviceAccount.projectId}/firestore/data`);

    console.log('\n✅ Verification completed successfully!');

  } catch (error) {
    console.error('❌ Error during verification:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  verifySetup()
    .then(() => {
      console.log('\n🏁 Verification script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Verification script failed:', error);
      process.exit(1);
    });
}

module.exports = { verifySetup };
