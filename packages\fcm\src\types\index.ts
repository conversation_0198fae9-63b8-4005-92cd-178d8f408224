/**
 * Shared FCM Types
 * 
 * Type definitions for Firebase Cloud Messaging that can be used
 * across both client and server environments.
 */

// ========================================
// CORE FCM TYPES
// ========================================

export interface FCMConfig {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
  vapidKey?: string;
}

export interface NotificationPayload {
  title: string;
  body: string;
  icon?: string;
  image?: string;
  badge?: string;
  sound?: string;
  tag?: string;
  color?: string;
  clickAction?: string;
  bodyLocKey?: string;
  bodyLocArgs?: string[];
  titleLocKey?: string;
  titleLocArgs?: string[];
}

export interface NotificationData {
  [key: string]: string;
}

export interface FCMMessage {
  notification?: NotificationPayload;
  data?: NotificationData;
  android?: {
    priority?: "normal" | "high";
    notification?: {
      title?: string;
      body?: string;
      icon?: string;
      color?: string;
      sound?: string;
      tag?: string;
      clickAction?: string;
      bodyLocKey?: string;
      bodyLocArgs?: string[];
      titleLocKey?: string;
      titleLocArgs?: string[];
      channelId?: string;
      ticker?: string;
      sticky?: boolean;
      eventTime?: string;
      localOnly?: boolean;
      notificationPriority?: "PRIORITY_MIN" | "PRIORITY_LOW" | "PRIORITY_DEFAULT" | "PRIORITY_HIGH" | "PRIORITY_MAX";
      defaultSound?: boolean;
      defaultVibrateTimings?: boolean;
      defaultLightSettings?: boolean;
      vibrateTimings?: string[];
      visibility?: "PRIVATE" | "PUBLIC" | "SECRET";
      notificationCount?: number;
      lightSettings?: {
        color: {
          red: number;
          green: number;
          blue: number;
          alpha: number;
        };
        lightOnDuration: string;
        lightOffDuration: string;
      };
    };
    fcmOptions?: {
      analyticsLabel?: string;
    };
    data?: NotificationData;
  };
  webpush?: {
    headers?: { [key: string]: string };
    data?: NotificationData;
    notification?: {
      title?: string;
      body?: string;
      icon?: string;
      badge?: string;
      image?: string;
      lang?: string;
      tag?: string;
      dir?: "auto" | "ltr" | "rtl";
      renotify?: boolean;
      requireInteraction?: boolean;
      silent?: boolean;
      timestamp?: number;
      vibrate?: number[];
      actions?: Array<{
        action: string;
        title: string;
        icon?: string;
      }>;
    };
    fcmOptions?: {
      link?: string;
      analyticsLabel?: string;
    };
  };
  apns?: {
    headers?: { [key: string]: string };
    payload?: {
      aps?: {
        alert?: {
          title?: string;
          subtitle?: string;
          body?: string;
          locKey?: string;
          locArgs?: string[];
          titleLocKey?: string;
          titleLocArgs?: string[];
          subtitleLocKey?: string;
          subtitleLocArgs?: string[];
          actionLocKey?: string;
          launchImage?: string;
        };
        badge?: number;
        sound?: string | {
          critical?: boolean;
          name?: string;
          volume?: number;
        };
        contentAvailable?: boolean;
        mutableContent?: boolean;
        category?: string;
        threadId?: string;
        targetContentId?: string;
        interruptionLevel?: "passive" | "active" | "timeSensitive" | "critical";
        relevanceScore?: number;
        filterCriteria?: string;
        staleDate?: number;
        contentState?: { [key: string]: any };
        timestamp?: number;
        event?: "update" | "end";
        dismissalDate?: number;
        attributesType?: "timer";
        attributes?: {
          start?: number;
          stale?: number;
          end?: number;
        };
      };
      [key: string]: any;
    };
    fcmOptions?: {
      analyticsLabel?: string;
      image?: string;
    };
  };
}

// ========================================
// NOTIFICATION TYPES
// ========================================

export type NotificationType = 
  | "contact_form"
  | "newsletter_signup"
  | "user_welcome"
  | "user_goodbye"
  | "campaign_started"
  | "campaign_completed"
  | "campaign_status_changed"
  | "daily_digest"
  | "weekly_report"
  | "system_alert"
  | "custom"
  | "bulk"
  | "retry_success"
  | "retry_scheduled"
  | "retry_failed";

export type NotificationPriority = "low" | "normal" | "high" | "urgent";

export type NotificationStatus = "pending" | "sent" | "delivered" | "failed" | "cancelled";

// ========================================
// DEVICE AND TOPIC TYPES
// ========================================

export interface FCMDevice {
  id: string;
  userId: string;
  token: string;
  platform: "web" | "android" | "ios";
  deviceInfo: {
    userAgent?: string;
    language?: string;
    timezone?: string;
    appVersion?: string;
    osVersion?: string;
    deviceModel?: string;
  };
  isActive: boolean;
  lastSeen: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface FCMTopic {
  name: string;
  displayName: string;
  description?: string;
  subscriberCount: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ========================================
// PREFERENCE TYPES
// ========================================

export interface NotificationPreferences {
  userId: string;
  email: string;
  fcmTokens: string[];
  topics: string[];
  preferences: {
    contactForm: boolean;
    newsletter: boolean;
    userUpdates: boolean;
    campaigns: boolean;
    dailyDigest: boolean;
    weeklyReport: boolean;
    systemAlerts: boolean;
    marketing: boolean;
  };
  timezone?: string;
  language?: string;
  quietHours?: {
    enabled: boolean;
    start: string; // HH:mm format
    end: string;   // HH:mm format
  };
  createdAt: Date;
  updatedAt: Date;
}

// ========================================
// RESPONSE TYPES
// ========================================

export interface FCMResponse {
  success: boolean;
  messageId?: string;
  error?: string;
  failureCount?: number;
  successCount?: number;
  responses?: Array<{
    success: boolean;
    messageId?: string;
    error?: string;
  }>;
}

export interface NotificationResponse extends FCMResponse {}

// ========================================
// HOOK TYPES
// ========================================

export interface FCMHookState {
  isInitialized: boolean;
  isSupported: boolean;
  permission: NotificationPermission | null;
  token: string | null;
  error: string | null;
  isLoading: boolean;
}

export interface NotificationPermissionState {
  permission: NotificationPermission | null;
  isSupported: boolean;
  isLoading: boolean;
  error: string | null;
}

// ========================================
// UTILITY TYPES
// ========================================

export interface FCMEnvironment {
  apiKey: string;
  authDomain: string;
  projectId: string;
  storageBucket: string;
  messagingSenderId: string;
  appId: string;
  vapidKey?: string;
}

export interface FCMValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
