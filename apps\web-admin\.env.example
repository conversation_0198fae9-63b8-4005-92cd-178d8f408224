# ========================================
# ADMIN APP SPECIFIC ENVIRONMENT VARIABLES
# Copy this file to .env.local and fill in your values
# ========================================



# Firebase Client Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=*********
NEXT_PUBLIC_FIREBASE_APP_ID=1:*********:web:abcdef123456
NEXT_PUBLIC_FIREBASE_VAPID_KEY=BLBz-HXFn0hHgLfnw8HxJQDCLOzuiTxwITjuS2Dk6RgaGbpKlkQxZF_UxHAneAID1KT1MxgN8m-PVj0B5F_R8Oc

# Firebase Admin Configuration (Server-side only)
FIREBASE_ADMIN_PROJECT_ID=your_project_id
FIREBASE_ADMIN_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYour private key here\n-----END PRIVATE KEY-----\n"
FIREBASE_ADMIN_CLIENT_EMAIL=firebase-adminsdk-xxxxx@your_project.iam.gserviceaccount.com

# App Identity
NEXT_PUBLIC_APP_NAME=Encreasl Admin Dashboard
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_APP_URL=https://admin.encreasl.com

# Admin-specific Features
NEXT_PUBLIC_ENABLE_USER_MANAGEMENT=true
NEXT_PUBLIC_ENABLE_ANALYTICS_DASHBOARD=true
NEXT_PUBLIC_ENABLE_CONTENT_MANAGEMENT=true
NEXT_PUBLIC_ENABLE_CAMPAIGN_MANAGEMENT=true

# Security & Access Control
NEXT_PUBLIC_REQUIRE_2FA=true
NEXT_PUBLIC_SESSION_TIMEOUT=3600
NEXT_PUBLIC_MAX_LOGIN_ATTEMPTS=5

# Development & Debugging
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_SHOW_DEV_TOOLS=true

# Admin API Configuration
NEXT_PUBLIC_ADMIN_API_BASE_URL=http://localhost:3001/api

# Database Access (Admin only)
ADMIN_DATABASE_URL=postgresql://username:password@localhost:5432/encreasl_admin
ADMIN_DATABASE_POOL_SIZE=10

# Third-party Admin Services
ADMIN_SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/webhook/url
ADMIN_DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your/webhook/url

# Monitoring & Logging
ADMIN_LOG_LEVEL=info
ADMIN_ENABLE_AUDIT_LOGS=true

# Email Notifications (Admin)
ADMIN_NOTIFICATION_EMAIL=<EMAIL>
ADMIN_ALERT_EMAIL=<EMAIL>

# Development
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_SHOW_DEV_TOOLS=true

# Security Headers
ADMIN_ENABLE_CSP=true
ADMIN_ENABLE_HSTS=true
