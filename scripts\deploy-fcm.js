#!/usr/bin/env node

/**
 * FCM Deployment Script
 * 
 * Automated deployment script for Firebase Cloud Functions and FCM setup
 * in the Encreasl monorepo. Handles building, testing, and deploying
 * all FCM-related components.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// ========================================
// CONFIGURATION
// ========================================

const CONFIG = {
  // Project settings
  PROJECT_ID: process.env.FIREBASE_PROJECT_ID || 'your-project-id',
  ENVIRONMENT: process.env.NODE_ENV || 'production',
  
  // Deployment options
  DEPLOY_FUNCTIONS: process.env.DEPLOY_FUNCTIONS !== 'false',
  DEPLOY_HOSTING: process.env.DEPLOY_HOSTING !== 'false',
  DEPLOY_FIRESTORE: process.env.DEPLOY_FIRESTORE !== 'false',
  
  // Build options
  SKIP_TESTS: process.env.SKIP_TESTS === 'true',
  SKIP_LINT: process.env.SKIP_LINT === 'true',
  VERBOSE: process.env.VERBOSE === 'true',
  
  // Paths
  ROOT_DIR: process.cwd(),
  FUNCTIONS_DIR: path.join(process.cwd(), 'functions'),
  DOCS_DIR: path.join(process.cwd(), 'docs'),
};

// ========================================
// UTILITY FUNCTIONS
// ========================================

function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const prefix = {
    info: '📝',
    success: '✅',
    warning: '⚠️',
    error: '❌',
    step: '🚀',
  }[type] || '📝';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function execCommand(command, options = {}) {
  const { cwd = CONFIG.ROOT_DIR, silent = false } = options;
  
  if (CONFIG.VERBOSE && !silent) {
    log(`Executing: ${command}`, 'info');
  }
  
  try {
    const result = execSync(command, {
      cwd,
      stdio: silent ? 'pipe' : 'inherit',
      encoding: 'utf8',
    });
    return result;
  } catch (error) {
    log(`Command failed: ${command}`, 'error');
    log(`Error: ${error.message}`, 'error');
    throw error;
  }
}

function checkPrerequisites() {
  log('Checking prerequisites...', 'step');
  
  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  if (majorVersion < 18) {
    throw new Error(`Node.js 18+ required, found ${nodeVersion}`);
  }
  log(`Node.js version: ${nodeVersion}`, 'success');
  
  // Check pnpm
  try {
    const pnpmVersion = execCommand('pnpm --version', { silent: true }).trim();
    log(`pnpm version: ${pnpmVersion}`, 'success');
  } catch (error) {
    throw new Error('pnpm is required but not installed');
  }
  
  // Check Firebase CLI
  try {
    const firebaseVersion = execCommand('firebase --version', { silent: true }).trim();
    log(`Firebase CLI version: ${firebaseVersion}`, 'success');
  } catch (error) {
    throw new Error('Firebase CLI is required but not installed');
  }
  
  // Check Firebase authentication
  try {
    execCommand('firebase projects:list', { silent: true });
    log('Firebase authentication verified', 'success');
  } catch (error) {
    throw new Error('Firebase authentication failed. Run: firebase login');
  }
  
  // Check if functions directory exists
  if (!fs.existsSync(CONFIG.FUNCTIONS_DIR)) {
    throw new Error('Functions directory not found');
  }
  
  log('All prerequisites met', 'success');
}

function validateEnvironment() {
  log('Validating environment...', 'step');
  
  const requiredEnvVars = [
    'FIREBASE_ADMIN_PROJECT_ID',
    'FIREBASE_ADMIN_PRIVATE_KEY',
    'FIREBASE_ADMIN_CLIENT_EMAIL',
  ];
  
  const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    log(`Missing environment variables: ${missingVars.join(', ')}`, 'warning');
    log('Some functions may not work properly without these variables', 'warning');
  } else {
    log('Environment variables validated', 'success');
  }
  
  // Check Firebase project
  try {
    const currentProject = execCommand('firebase use', { silent: true }).trim();
    log(`Current Firebase project: ${currentProject}`, 'info');
  } catch (error) {
    log('No Firebase project selected. Use: firebase use <project-id>', 'warning');
  }
}

function installDependencies() {
  log('Installing dependencies...', 'step');
  
  // Install root dependencies
  execCommand('pnpm install');
  
  // Install functions dependencies
  execCommand('npm install', { cwd: CONFIG.FUNCTIONS_DIR });
  
  log('Dependencies installed', 'success');
}

function buildPackages() {
  log('Building shared packages...', 'step');
  
  // Build FCM package
  try {
    execCommand('pnpm build --filter=@encreasl/fcm');
    log('FCM package built', 'success');
  } catch (error) {
    log('FCM package build failed', 'warning');
  }
  
  // Build auth package
  try {
    execCommand('pnpm build --filter=@encreasl/auth');
    log('Auth package built', 'success');
  } catch (error) {
    log('Auth package build failed', 'warning');
  }
  
  log('Shared packages built', 'success');
}

function lintAndTypeCheck() {
  if (CONFIG.SKIP_LINT) {
    log('Skipping lint and type check', 'warning');
    return;
  }
  
  log('Running lint and type check...', 'step');
  
  // Lint functions
  try {
    execCommand('npm run lint', { cwd: CONFIG.FUNCTIONS_DIR });
    log('Functions linting passed', 'success');
  } catch (error) {
    log('Functions linting failed', 'error');
    throw error;
  }
  
  // Type check functions
  try {
    execCommand('npm run type-check', { cwd: CONFIG.FUNCTIONS_DIR });
    log('Functions type check passed', 'success');
  } catch (error) {
    log('Functions type check failed', 'error');
    throw error;
  }
}

function runTests() {
  if (CONFIG.SKIP_TESTS) {
    log('Skipping tests', 'warning');
    return;
  }
  
  log('Running tests...', 'step');
  
  try {
    execCommand('npm run test', { cwd: CONFIG.FUNCTIONS_DIR });
    log('Tests passed', 'success');
  } catch (error) {
    log('Tests failed', 'error');
    throw error;
  }
}

function buildFunctions() {
  log('Building functions...', 'step');
  
  try {
    execCommand('npm run build', { cwd: CONFIG.FUNCTIONS_DIR });
    log('Functions built successfully', 'success');
  } catch (error) {
    log('Functions build failed', 'error');
    throw error;
  }
}

function deployFunctions() {
  if (!CONFIG.DEPLOY_FUNCTIONS) {
    log('Skipping functions deployment', 'warning');
    return;
  }
  
  log('Deploying Cloud Functions...', 'step');
  
  try {
    const deployCommand = `firebase deploy --only functions --project ${CONFIG.PROJECT_ID}`;
    execCommand(deployCommand);
    log('Functions deployed successfully', 'success');
  } catch (error) {
    log('Functions deployment failed', 'error');
    throw error;
  }
}

function deployFirestore() {
  if (!CONFIG.DEPLOY_FIRESTORE) {
    log('Skipping Firestore deployment', 'warning');
    return;
  }
  
  log('Deploying Firestore rules and indexes...', 'step');
  
  try {
    const deployCommand = `firebase deploy --only firestore --project ${CONFIG.PROJECT_ID}`;
    execCommand(deployCommand);
    log('Firestore deployed successfully', 'success');
  } catch (error) {
    log('Firestore deployment failed', 'error');
    throw error;
  }
}

function deployHosting() {
  if (!CONFIG.DEPLOY_HOSTING) {
    log('Skipping hosting deployment', 'warning');
    return;
  }
  
  log('Deploying hosting...', 'step');
  
  try {
    // Build admin app
    execCommand('pnpm build:admin');
    
    // Deploy hosting
    const deployCommand = `firebase deploy --only hosting --project ${CONFIG.PROJECT_ID}`;
    execCommand(deployCommand);
    log('Hosting deployed successfully', 'success');
  } catch (error) {
    log('Hosting deployment failed', 'error');
    throw error;
  }
}

function verifyDeployment() {
  log('Verifying deployment...', 'step');
  
  try {
    // List deployed functions
    const functions = execCommand(`firebase functions:list --project ${CONFIG.PROJECT_ID}`, { silent: true });
    const functionCount = (functions.match(/Function/g) || []).length;
    log(`Deployed functions: ${functionCount}`, 'info');
    
    // Check function logs for errors
    try {
      execCommand(`firebase functions:log --project ${CONFIG.PROJECT_ID} --lines 10`, { silent: true });
      log('Function logs accessible', 'success');
    } catch (error) {
      log('Could not access function logs', 'warning');
    }
    
    log('Deployment verification completed', 'success');
  } catch (error) {
    log('Deployment verification failed', 'warning');
  }
}

// ========================================
// MAIN DEPLOYMENT FUNCTION
// ========================================

async function main() {
  const startTime = Date.now();
  
  try {
    log('🚀 Starting FCM deployment process...', 'step');
    log(`Environment: ${CONFIG.ENVIRONMENT}`, 'info');
    log(`Project: ${CONFIG.PROJECT_ID}`, 'info');
    
    // Pre-deployment checks
    checkPrerequisites();
    validateEnvironment();
    
    // Build process
    installDependencies();
    buildPackages();
    lintAndTypeCheck();
    runTests();
    buildFunctions();
    
    // Deployment process
    deployFunctions();
    deployFirestore();
    deployHosting();
    
    // Post-deployment
    verifyDeployment();
    
    const duration = Math.round((Date.now() - startTime) / 1000);
    log(`🎉 Deployment completed successfully in ${duration}s`, 'success');
    
  } catch (error) {
    const duration = Math.round((Date.now() - startTime) / 1000);
    log(`💥 Deployment failed after ${duration}s`, 'error');
    log(`Error: ${error.message}`, 'error');
    process.exit(1);
  }
}

// ========================================
// CLI HANDLING
// ========================================

if (require.main === module) {
  // Parse command line arguments
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
🚀 FCM Deployment Script

Usage: node scripts/deploy-fcm.js [options]

Options:
  --help, -h              Show this help message
  --skip-tests           Skip running tests
  --skip-lint            Skip linting and type checking
  --functions-only       Deploy only Cloud Functions
  --firestore-only       Deploy only Firestore rules
  --hosting-only         Deploy only hosting
  --verbose              Enable verbose logging

Environment Variables:
  FIREBASE_PROJECT_ID    Firebase project ID (required)
  DEPLOY_FUNCTIONS       Deploy functions (default: true)
  DEPLOY_HOSTING         Deploy hosting (default: true)
  DEPLOY_FIRESTORE       Deploy Firestore (default: true)
  SKIP_TESTS            Skip tests (default: false)
  SKIP_LINT             Skip linting (default: false)
  VERBOSE               Enable verbose logging (default: false)

Examples:
  node scripts/deploy-fcm.js
  node scripts/deploy-fcm.js --functions-only --skip-tests
  FIREBASE_PROJECT_ID=my-project node scripts/deploy-fcm.js
    `);
    process.exit(0);
  }
  
  // Handle specific deployment options
  if (args.includes('--functions-only')) {
    CONFIG.DEPLOY_HOSTING = false;
    CONFIG.DEPLOY_FIRESTORE = false;
  }
  
  if (args.includes('--firestore-only')) {
    CONFIG.DEPLOY_FUNCTIONS = false;
    CONFIG.DEPLOY_HOSTING = false;
  }
  
  if (args.includes('--hosting-only')) {
    CONFIG.DEPLOY_FUNCTIONS = false;
    CONFIG.DEPLOY_FIRESTORE = false;
  }
  
  if (args.includes('--skip-tests')) {
    CONFIG.SKIP_TESTS = true;
  }
  
  if (args.includes('--skip-lint')) {
    CONFIG.SKIP_LINT = true;
  }
  
  if (args.includes('--verbose')) {
    CONFIG.VERBOSE = true;
  }
  
  main();
}

module.exports = {
  main,
  CONFIG,
};
