{"name": "@encreasl/auth", "version": "0.1.0", "private": true, "main": "./src/index.ts", "types": "./src/index.ts", "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}, "./server": {"types": "./src/server/index.ts", "default": "./src/server/index.ts"}}, "scripts": {"build": "tsc --noEmit", "type-check": "tsc --noEmit"}, "dependencies": {"firebase": "^10.7.1", "zod": "^3.22.4"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0"}, "devDependencies": {"@encreasl/typescript-config": "workspace:*", "@types/react": "^19.0.0", "react": "^19.1.0", "typescript": "^5.3.3"}}