/**
 * Scheduled Notification Triggers
 *
 * Cloud Functions that run on schedules to send periodic notifications,
 * cleanup old data, and perform maintenance tasks.
 */
export declare const sendDailyDigest: import("firebase-functions/v2/scheduler").ScheduleFunction;
export declare const sendWeeklyReport: import("firebase-functions/v2/scheduler").ScheduleFunction;
export declare const cleanupOldNotifications: import("firebase-functions/v2/scheduler").ScheduleFunction;
//# sourceMappingURL=scheduled-notifications.d.ts.map