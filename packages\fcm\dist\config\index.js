"use strict";
/**
 * FCM Configuration Utilities
 *
 * Shared configuration utilities for Firebase Cloud Messaging
 * that work across different environments and applications.
 */
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.TOPIC_CONFIGS = exports.FCM_TOPICS = exports.FCM_SETTINGS = exports.DEFAULT_FCM_CONFIG = void 0;
exports.createFCMConfig = createFCMConfig;
exports.getFCMConfig = getFCMConfig;
exports.validateFCMConfig = validateFCMConfig;
exports.validateFCMEnvironment = validateFCMEnvironment;
exports.isFCMConfigured = isFCMConfigured;
exports.getFCMConfigStatus = getFCMConfigStatus;
var zod_1 = require("zod");
// ========================================
// VALIDATION SCHEMAS
// ========================================
var FCMConfigSchema = zod_1.z.object({
    apiKey: zod_1.z.string().min(1, "API key is required"),
    authDomain: zod_1.z.string().min(1, "Auth domain is required"),
    projectId: zod_1.z.string().min(1, "Project ID is required"),
    storageBucket: zod_1.z.string().min(1, "Storage bucket is required"),
    messagingSenderId: zod_1.z.string().min(1, "Messaging sender ID is required"),
    appId: zod_1.z.string().min(1, "App ID is required"),
    vapidKey: zod_1.z.string().optional(),
});
var FCMEnvironmentSchema = zod_1.z.object({
    NEXT_PUBLIC_FIREBASE_API_KEY: zod_1.z.string().min(1),
    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: zod_1.z.string().min(1),
    NEXT_PUBLIC_FIREBASE_PROJECT_ID: zod_1.z.string().min(1),
    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: zod_1.z.string().min(1),
    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: zod_1.z.string().min(1),
    NEXT_PUBLIC_FIREBASE_APP_ID: zod_1.z.string().min(1),
    NEXT_PUBLIC_FIREBASE_VAPID_KEY: zod_1.z.string().optional(),
});
// ========================================
// CONFIGURATION FUNCTIONS
// ========================================
/**
 * Create FCM configuration from environment variables
 */
function createFCMConfig(env) {
    var config = {
        apiKey: env.NEXT_PUBLIC_FIREBASE_API_KEY || '',
        authDomain: env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN || '',
        projectId: env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || '',
        storageBucket: env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET || '',
        messagingSenderId: env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID || '',
        appId: env.NEXT_PUBLIC_FIREBASE_APP_ID || '',
        vapidKey: env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
    };
    var result = FCMConfigSchema.safeParse(config);
    if (!result.success) {
        throw new Error("Invalid FCM configuration: ".concat(result.error.issues.map(function (i) { return i.message; }).join(', ')));
    }
    return result.data;
}
/**
 * Get FCM configuration from process.env
 */
function getFCMConfig() {
    return createFCMConfig(process.env);
}
/**
 * Validate FCM configuration
 */
function validateFCMConfig(config) {
    var result = FCMConfigSchema.safeParse(config);
    if (result.success) {
        var warnings = [];
        // Check for optional but recommended fields
        if (!config.vapidKey) {
            warnings.push("VAPID key is not configured - web push notifications may not work properly");
        }
        return {
            isValid: true,
            errors: [],
            warnings: warnings,
        };
    }
    return {
        isValid: false,
        errors: result.error.issues.map(function (issue) { return "".concat(issue.path.join('.'), ": ").concat(issue.message); }),
        warnings: [],
    };
}
/**
 * Validate FCM environment variables
 */
function validateFCMEnvironment(env) {
    if (env === void 0) { env = process.env; }
    var result = FCMEnvironmentSchema.safeParse(env);
    if (result.success) {
        var warnings = [];
        // Check for optional but recommended fields
        if (!env.NEXT_PUBLIC_FIREBASE_VAPID_KEY) {
            warnings.push("NEXT_PUBLIC_FIREBASE_VAPID_KEY is not set - web push notifications may not work properly");
        }
        return {
            isValid: true,
            errors: [],
            warnings: warnings,
        };
    }
    return {
        isValid: false,
        errors: result.error.issues.map(function (issue) { return "".concat(issue.path.join('.'), ": ").concat(issue.message); }),
        warnings: [],
    };
}
// ========================================
// ENVIRONMENT HELPERS
// ========================================
/**
 * Check if FCM is properly configured in the current environment
 */
function isFCMConfigured(env) {
    if (env === void 0) { env = process.env; }
    var validation = validateFCMEnvironment(env);
    return validation.isValid;
}
/**
 * Get FCM configuration status with detailed information
 */
function getFCMConfigStatus(env) {
    if (env === void 0) { env = process.env; }
    var validation = validateFCMEnvironment(env);
    return {
        isConfigured: validation.isValid,
        hasVapidKey: !!env.NEXT_PUBLIC_FIREBASE_VAPID_KEY,
        projectId: env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || null,
        errors: validation.errors,
        warnings: validation.warnings,
    };
}
// ========================================
// DEFAULT CONFIGURATIONS
// ========================================
/**
 * Default FCM configuration for development
 */
exports.DEFAULT_FCM_CONFIG = {
    // These should be overridden by environment variables
    apiKey: '',
    authDomain: '',
    projectId: '',
    storageBucket: '',
    messagingSenderId: '',
    appId: '',
};
/**
 * FCM feature flags and settings
 */
exports.FCM_SETTINGS = {
    // Enable/disable FCM features
    ENABLE_BACKGROUND_MESSAGING: true,
    ENABLE_NOTIFICATION_ANALYTICS: true,
    ENABLE_TOPIC_SUBSCRIPTIONS: true,
    // Default notification settings
    DEFAULT_NOTIFICATION_ICON: '/icons/notification-icon.png',
    DEFAULT_NOTIFICATION_BADGE: '/icons/notification-badge.png',
    // Service worker settings
    SERVICE_WORKER_PATH: '/firebase-messaging-sw.js',
    SERVICE_WORKER_SCOPE: '/',
    // Retry settings
    MAX_RETRY_ATTEMPTS: 3,
    RETRY_DELAY_MS: 1000,
    // Token refresh settings
    TOKEN_REFRESH_INTERVAL_MS: 24 * 60 * 60 * 1000, // 24 hours
};
// ========================================
// TOPIC CONFIGURATIONS
// ========================================
/**
 * Predefined FCM topics for the application
 */
exports.FCM_TOPICS = {
    // Admin topics
    ADMIN_NOTIFICATIONS: 'admin-notifications',
    ADMIN_ALERTS: 'admin-alerts',
    ADMIN_REPORTS: 'admin-reports',
    // User topics
    USER_UPDATES: 'user-updates',
    NEWSLETTER: 'newsletter',
    CAMPAIGNS: 'campaigns',
    MARKETING: 'marketing',
    // System topics
    SYSTEM_ALERTS: 'system-alerts',
    MAINTENANCE: 'maintenance',
    // General topics
    GENERAL: 'general',
    ANNOUNCEMENTS: 'announcements',
};
/**
 * Topic configurations with metadata
 */
exports.TOPIC_CONFIGS = (_a = {},
    _a[exports.FCM_TOPICS.ADMIN_NOTIFICATIONS] = {
        displayName: 'Admin Notifications',
        description: 'Important notifications for administrators',
        defaultSubscribed: false,
        requiresPermission: true,
    },
    _a[exports.FCM_TOPICS.ADMIN_ALERTS] = {
        displayName: 'Admin Alerts',
        description: 'Critical alerts requiring immediate attention',
        defaultSubscribed: false,
        requiresPermission: true,
    },
    _a[exports.FCM_TOPICS.USER_UPDATES] = {
        displayName: 'Account Updates',
        description: 'Updates about your account and profile',
        defaultSubscribed: true,
        requiresPermission: false,
    },
    _a[exports.FCM_TOPICS.NEWSLETTER] = {
        displayName: 'Newsletter',
        description: 'Weekly newsletter and updates',
        defaultSubscribed: false,
        requiresPermission: false,
    },
    _a[exports.FCM_TOPICS.CAMPAIGNS] = {
        displayName: 'Marketing Campaigns',
        description: 'New marketing campaigns and promotions',
        defaultSubscribed: false,
        requiresPermission: false,
    },
    _a[exports.FCM_TOPICS.MARKETING] = {
        displayName: 'Marketing Updates',
        description: 'Marketing tips, insights, and best practices',
        defaultSubscribed: false,
        requiresPermission: false,
    },
    _a[exports.FCM_TOPICS.SYSTEM_ALERTS] = {
        displayName: 'System Alerts',
        description: 'System maintenance and service updates',
        defaultSubscribed: true,
        requiresPermission: false,
    },
    _a[exports.FCM_TOPICS.GENERAL] = {
        displayName: 'General Notifications',
        description: 'General updates and announcements',
        defaultSubscribed: true,
        requiresPermission: false,
    },
    _a);
//# sourceMappingURL=index.js.map