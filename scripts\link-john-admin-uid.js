/**
 * Link John Admin User with Firebase Auth UID
 * 
 * This script updates the Firestore user document to use the correct
 * Firebase Auth UID: T6W1Urk96RaYBItw3ecjdlQbvoH3
 */

const admin = require('firebase-admin');
const path = require('path');

// Load environment variables from the root .env.local file
require('dotenv').config({ path: path.resolve(__dirname, '../.env.local') });

// Admin user details
const ADMIN_EMAIL = '<EMAIL>';
const FIREBASE_AUTH_UID = 'T6W1Urk96RaYBItw3ecjdlQbvoH3'; // From Firebase Console

// Firebase Admin configuration
const serviceAccount = {
  projectId: process.env.FIREBASE_ADMIN_PROJECT_ID,
  privateKey: process.env.FIREBASE_ADMIN_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  clientEmail: process.env.FIREBASE_ADMIN_CLIENT_EMAIL,
};

async function linkJohnAdminUID() {
  try {
    console.log('🔗 Linking John Admin User with Firebase Auth UID...');
    console.log('📧 Email:', ADMIN_EMAIL);
    console.log('🆔 Firebase Auth UID:', FIREBASE_AUTH_UID);
    console.log('📊 Project ID:', serviceAccount.projectId);

    // Initialize Firebase Admin
    if (!admin.apps.length) {
      admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.projectId,
      });
    }

    const db = admin.firestore();
    const auth = admin.auth();

    // Step 1: Find existing user document
    console.log('\n1️⃣  Finding existing user document...');
    const userQuery = await db.collection('users')
      .where('email', '==', ADMIN_EMAIL)
      .limit(1)
      .get();
    
    if (userQuery.empty) {
      console.log('   ❌ User document not found in Firestore');
      return;
    }

    const existingDoc = userQuery.docs[0];
    const existingData = existingDoc.data();
    console.log('   ✅ Found existing user document');
    console.log(`   🆔 Current Document ID: ${existingDoc.id}`);
    console.log(`   👤 Display Name: ${existingData.displayName}`);

    // Step 2: Create new document with Firebase Auth UID as document ID
    console.log('\n2️⃣  Creating new document with Firebase Auth UID...');
    
    const updatedUserData = {
      ...existingData,
      id: FIREBASE_AUTH_UID, // Update the id field to match Firebase Auth UID
      updatedAt: admin.firestore.Timestamp.now(),
      version: admin.firestore.FieldValue.increment(1)
    };

    // Create the new document with Firebase Auth UID as document ID
    await db.collection('users').doc(FIREBASE_AUTH_UID).set(updatedUserData);
    console.log('   ✅ Created new user document with Firebase Auth UID');

    // Step 3: Delete old document (if different)
    if (existingDoc.id !== FIREBASE_AUTH_UID) {
      console.log('\n3️⃣  Removing old document...');
      await existingDoc.ref.delete();
      console.log('   ✅ Old document removed');
    } else {
      console.log('\n3️⃣  Document ID already matches Firebase Auth UID - no cleanup needed');
    }

    // Step 4: Try to set custom claims (may fail due to permissions)
    console.log('\n4️⃣  Attempting to set custom claims...');
    try {
      await auth.setCustomUserClaims(FIREBASE_AUTH_UID, {
        admin: true,
        role: 'admin',
        permissions: ["*"],
        userId: FIREBASE_AUTH_UID
      });
      console.log('   ✅ Custom claims set successfully');
    } catch (claimsError) {
      console.log('   ⚠️  Could not set custom claims (permission issue)');
      console.log('   📝 You can set these manually in Firebase Console if needed:');
      console.log('   🔗 https://console.firebase.google.com/project/encreasl-daa43/authentication/users');
      console.log('   🏷️  Suggested custom claims:');
      console.log('      {"admin": true, "role": "admin", "permissions": ["*"]}');
    }

    // Step 5: Verify the setup
    console.log('\n5️⃣  Verifying final setup...');
    const finalDoc = await db.collection('users').doc(FIREBASE_AUTH_UID).get();
    if (finalDoc.exists) {
      const finalData = finalDoc.data();
      console.log('   ✅ User document verified');
      console.log(`   🆔 Document ID: ${finalDoc.id}`);
      console.log(`   📧 Email: ${finalData.email}`);
      console.log(`   👤 Display Name: ${finalData.displayName}`);
      console.log(`   👑 Role: ${finalData.role}`);
      console.log(`   🔐 Permissions: ${finalData.permissions?.join(', ') || 'None'}`);
      console.log(`   ✅ Active: ${finalData.isActive ? 'Yes' : 'No'}`);
    }

    // Success summary
    console.log('\n🎉 SUCCESS! John Admin User Linked');
    console.log('=====================================');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log(`🆔 Firebase Auth UID: ${FIREBASE_AUTH_UID}`);
    console.log(`🆔 Firestore Document ID: ${FIREBASE_AUTH_UID}`);
    console.log(`👑 Role: admin`);
    console.log(`🔐 Permissions: *`);
    console.log(`✅ Active: true`);
    
    console.log('\n📝 Ready for Login!');
    console.log('The user can now log in to the admin panel with:');
    console.log(`📧 Email: ${ADMIN_EMAIL}`);
    console.log('🔒 Password: @Iamachessgrandmaster23');
    
    console.log('\n🔗 Test Login:');
    console.log('Go to your admin login page and test the credentials');

  } catch (error) {
    console.error('\n❌ Error linking John admin user:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  linkJohnAdminUID()
    .then(() => {
      console.log('\n🏁 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Script failed:', error);
      process.exit(1);
    });
}

module.exports = { linkJohnAdminUID };
